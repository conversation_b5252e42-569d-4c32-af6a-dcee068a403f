Design a user-friendly and visually appealing legal reference Android app named "Indian Law Library" for accessing Indian legal content only in Hindi language, with the app interface in English. This app aims to function as a comprehensive digital law book, providing easy access to legal books, their individual sections, and detailed descriptions. The primary target audience includes government employees, law students and legal professionals.
Core Functionality:
Digital Law Book: Provide access to a library of Indian legal books.
User Interface (UI) Design Principles:
User-Friendly: Intuitive navigation, easy interaction and mobile friendly.
Good Looking: Modern, clean, and visually appealing design.
Key UI Elements:
Header:
Name: "Indian Law" centered.
Profile photo (Left): The user's profile photo will be in the circle.
Search Component (Right): An icon-based search bar allowing users to search by book title, section title, and section content.
Display notification updates any add new book and any book amendment (if any).
Interactive/Animated Bottom navigation bar: The following items are displayed with clear icons and labels, in this order-
Favorites: To access saved sections. (Suggest a common heart icon).
Tags: To manage and view tagged sections. (Suggest a common tag icon).
Home: To display the list of legal books. (Suggest a common book icon).
Books library: list of all available books (Suggest a common books icon).
Profile: to manage profile and personal info.(Suggest a common profile icon)
Color Scheme: Primary: #4294ff, Secondary: #e6ecff. Ensure sufficient color contrast for accessibility.
Features List:
Profile Tab:
Profile Section: Displays the user's profile photo and name.
Profile Page: A direct link to the user's profile management page.
theme Mode Toggle
Legal Pages: Links to the Privacy Policy, About Us, Contact Us pages, Help center and terms of service.
Notification Preferences.
setting:
Logout/login Button: To securely log out and login of the user's account.
Home Tab:
Books: At the top, a list of books pinned for the user's personal use is displayed in vertical scrolling, followed by Recently Accessed books using panoramic scrolling, followed by Up to 5 latest newly created books using panoramic scrolling. All Books Library page link at the end.
Book Information: For each book, show the title accompanied by a law scale logo and the total number of sections underneath.
Progress Indicators: reading progress Included for each book by user.
Books library Tab: list of all available books library with search and short features.
Book Details Page:
Displays a comprehensive list of all sections belonging to the selected book.
To pin the book for personalized use, the pin icon in the top left.
Section Details Page:
Displays the complete content of the selected legal section.
Navigation: A clearly placed "Next Section" button.
Cross-References: Link related sections and books across different any particular section.
Actions:
"Add to Favorites" button for user personalized use.
"Tag" functionality for user personalized use, allowing users to select from existing predefined and user-created tags to categorize the section. A section can be associated with multiple tags.
"Share" option for sharing the section content via social media or messaging apps.
Include a "Settings" button for customizing reading preferences:
Font size, color.
Brightness.
Text color, size.
Text alignment.
Line spacing.
"Reset Reading Settings" option.
Favorites Tab:
Displays a list of all sections saved as favorites by the user.
Each entry shows the section title as the main text and the corresponding book title as a smaller subtitle below.
Tags Tab:
Users can view a list of all created tags by user.
Functionality to rename and remove existing tags.
Tapping on a tag display dropdown list inside all the legal sections associated with that tag.
A single legal section can be associated with multiple tags simultaneously.
Create User Account and Profile Features:
User Registration/Sign Up: Via email and password.
Login: Using registered email and password.
Password Reset: Functionality for users to recover their passwords.
Google Authentication: Option for login and signup using Google accounts.
Profile Page Management: Allows users to view and edit their name, profile photo, mobile number, email and address.
login required  for personalized access When a user performs:
to pin the book.
to favorite the section.
create tags.
to add the section to tag.
access notifications.
Note:App name Indian Law Library, Package name com.parultrade.indianlawlibrary
configuration with Firebase-
apiKey: "AIzaSyA-b4MTWRT8rb6NpOQbqLqKtcURq05YBJ8",
authDomain: "indian-law-library.firebaseapp.com",
projectId: "indian-law-library",
storageBucket: "indian-law-library.firebasestorage.app",
messagingSenderId: "************",
appId: "1:************:web:ab981682b704747e32cb91",
measurementId: "G-SD4RWQPDQQ"

All Firestore database list:
Books:
book_id: String (Consider using a unique, auto-generated ID)
title: String
total_sections: Number (Integer)
Category:String
Published_year
jurisdiction:String
is_new: Boolean (true/false)
created_at: Timestamp
updated_at: Timestamp
Sections:
section_id: String (Consider using a unique, auto-generated ID)
book_id: String (Foreign key referencing Books)
title: String
content: String (Potentially a long text field)
order_in_book: Number (Integer)
References:String (Foreign key referencing section_id)
amendment:String (Potentially a short text field)
created_at: Timestamp
updated_at: Timestamp
User profiles
User setting
User books (for pined book, reading progress & Recently access book)
User favorites section
User tag
User tags section
notifications 
User notification preferences
User search
Subscriptions


Implement a subscription model in my Indian Law Library app with the following specifications:
Free Trial
Provide 3 months of free premium access for users upon first account creation.
Premium Features (Post-Trial):After the trial ends, prompt the user to subscribe in order to continue accessing premium features:  
Pin a book for quick access
Favorite specific sections
Add sections to custom tags
Subscription Plans: Integrate the Razorpay payment gateway to offer the following subscription options :  
₹29 for 3 months
₹49 for 6 months
₹89 for 12 months
Razorpay Credentials:
Key ID: ***********************
Key Secret: 2rJ8jhSzAQC93TORjw3DfwR7
User Flow Requirements:
Clearly show when the free trial is active, and when it will expire
Upon expiration, prompt the user with a simple, clear subscription UI
Highlight the benefits of premium features in the subscription screen
Ensure the purchase flow is fast, secure, and mobile-friendly
Post-Purchase Behavior:
Immediately unlock premium features on successful payment
Store and validate subscription status locally and with server checks
Display “Premium Active” status in user profile/settings

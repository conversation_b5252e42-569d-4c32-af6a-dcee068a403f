import 'package:cloud_firestore/cloud_firestore.dart';

class UserNotificationPreferencesModel {
  final String uid;
  final bool newBooks;
  final bool amendments;
  final bool subscriptionReminders;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserNotificationPreferencesModel({
    required this.uid,
    required this.newBooks,
    required this.amendments,
    required this.subscriptionReminders,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserNotificationPreferencesModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return UserNotificationPreferencesModel(
      uid: doc.id,
      newBooks: data['new_books'] ?? true,
      amendments: data['amendments'] ?? true,
      subscriptionReminders: data['subscription_reminders'] ?? true,
      createdAt: (data['created_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updated_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'uid': uid,
      'new_books': newBooks,
      'amendments': amendments,
      'subscription_reminders': subscriptionReminders,
      'created_at': Timestamp.fromDate(createdAt),
      'updated_at': Timestamp.fromDate(updatedAt),
    };
  }

  UserNotificationPreferencesModel copyWith({
    String? uid,
    bool? newBooks,
    bool? amendments,
    bool? subscriptionReminders,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserNotificationPreferencesModel(
      uid: uid ?? this.uid,
      newBooks: newBooks ?? this.newBooks,
      amendments: amendments ?? this.amendments,
      subscriptionReminders: subscriptionReminders ?? this.subscriptionReminders,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

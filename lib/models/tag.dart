class Tag {
  final String id;
  final String name;
  final String color;
  final int bookCount;
  final DateTime createdAt;

  Tag({
    required this.id,
    required this.name,
    required this.color,
    this.bookCount = 0,
    required this.createdAt,
  });

  Tag copyWith({
    String? id,
    String? name,
    String? color,
    int? bookCount,
    DateTime? createdAt,
  }) {
    return Tag(
      id: id ?? this.id,
      name: name ?? this.name,
      color: color ?? this.color,
      bookCount: bookCount ?? this.bookCount,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

import 'package:cloud_firestore/cloud_firestore.dart';

class UserTagSectionModel {
  final String id;
  final String uid;
  final String tagId;
  final String sectionId;
  final DateTime createdAt;

  UserTagSectionModel({
    required this.id,
    required this.uid,
    required this.tagId,
    required this.sectionId,
    required this.createdAt,
  });

  factory UserTagSectionModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return UserTagSectionModel(
      id: doc.id,
      uid: data['uid'] ?? '',
      tagId: data['tag_id'] ?? '',
      sectionId: data['section_id'] ?? '',
      createdAt: (data['created_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'uid': uid,
      'tag_id': tagId,
      'section_id': sectionId,
      'created_at': Timestamp.fromDate(createdAt),
    };
  }

  UserTagSectionModel copyWith({
    String? id,
    String? uid,
    String? tagId,
    String? sectionId,
    DateTime? createdAt,
  }) {
    return UserTagSectionModel(
      id: id ?? this.id,
      uid: uid ?? this.uid,
      tagId: tagId ?? this.tagId,
      sectionId: sectionId ?? this.sectionId,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

import 'package:cloud_firestore/cloud_firestore.dart';

class NotificationModel {
  final String id;
  final String title;
  final String message;
  final String type; // 'new_book', 'amendment', 'subscription', 'general'
  final String? targetId; // book_id, section_id, etc.
  final String? targetType; // 'book', 'section', 'subscription'
  final bool isGlobal; // true for all users, false for specific users
  final List<String> targetUsers; // empty if isGlobal is true
  final DateTime createdAt;
  final DateTime? expiresAt;

  NotificationModel({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    this.targetId,
    this.targetType,
    required this.isGlobal,
    required this.targetUsers,
    required this.createdAt,
    this.expiresAt,
  });

  factory NotificationModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return NotificationModel(
      id: doc.id,
      title: data['title'] ?? '',
      message: data['message'] ?? '',
      type: data['type'] ?? 'general',
      targetId: data['target_id'],
      targetType: data['target_type'],
      isGlobal: data['is_global'] ?? false,
      targetUsers: List<String>.from(data['target_users'] ?? []),
      createdAt: (data['created_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
      expiresAt: (data['expires_at'] as Timestamp?)?.toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'message': message,
      'type': type,
      'target_id': targetId,
      'target_type': targetType,
      'is_global': isGlobal,
      'target_users': targetUsers,
      'created_at': Timestamp.fromDate(createdAt),
      'expires_at': expiresAt != null ? Timestamp.fromDate(expiresAt!) : null,
    };
  }

  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  NotificationModel copyWith({
    String? id,
    String? title,
    String? message,
    String? type,
    String? targetId,
    String? targetType,
    bool? isGlobal,
    List<String>? targetUsers,
    DateTime? createdAt,
    DateTime? expiresAt,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      targetId: targetId ?? this.targetId,
      targetType: targetType ?? this.targetType,
      isGlobal: isGlobal ?? this.isGlobal,
      targetUsers: targetUsers ?? this.targetUsers,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
    );
  }
}

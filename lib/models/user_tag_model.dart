import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

class UserTagModel {
  final String id;
  final String uid;
  final String name;
  final int colorValue;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserTagModel({
    required this.id,
    required this.uid,
    required this.name,
    required this.colorValue,
    required this.createdAt,
    required this.updatedAt,
  });

  Color get color => Color(colorValue);

  factory UserTagModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return UserTagModel(
      id: doc.id,
      uid: data['uid'] ?? '',
      name: data['name'] ?? '',
      colorValue: data['color_value'] ?? Colors.blue.value,
      createdAt: (data['created_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updated_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'uid': uid,
      'name': name,
      'color_value': colorValue,
      'created_at': Timestamp.fromDate(createdAt),
      'updated_at': Timestamp.fromDate(updatedAt),
    };
  }

  UserTagModel copyWith({
    String? id,
    String? uid,
    String? name,
    int? colorValue,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserTagModel(
      id: id ?? this.id,
      uid: uid ?? this.uid,
      name: name ?? this.name,
      colorValue: colorValue ?? this.colorValue,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

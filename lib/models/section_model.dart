import 'package:cloud_firestore/cloud_firestore.dart';

class SectionModel {
  final String id;
  final String bookId;
  final String title;
  final String content;
  final int orderInBook;
  final List<String> references;
  final String amendment;
  final DateTime createdAt;
  final DateTime updatedAt;

  SectionModel({
    required this.id,
    required this.bookId,
    required this.title,
    required this.content,
    required this.orderInBook,
    required this.references,
    required this.amendment,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SectionModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return SectionModel(
      id: doc.id,
      bookId: data['book_id'] ?? '',
      title: data['title'] ?? '',
      content: data['content'] ?? '',
      orderInBook: data['order_in_book'] ?? 0,
      references: List<String>.from(data['references'] ?? []),
      amendment: data['amendment'] ?? '',
      createdAt: (data['created_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updated_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'book_id': bookId,
      'title': title,
      'content': content,
      'order_in_book': orderInBook,
      'references': references,
      'amendment': amendment,
      'created_at': Timestamp.fromDate(createdAt),
      'updated_at': Timestamp.fromDate(updatedAt),
    };
  }

  SectionModel copyWith({
    String? id,
    String? bookId,
    String? title,
    String? content,
    int? orderInBook,
    List<String>? references,
    String? amendment,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SectionModel(
      id: id ?? this.id,
      bookId: bookId ?? this.bookId,
      title: title ?? this.title,
      content: content ?? this.content,
      orderInBook: orderInBook ?? this.orderInBook,
      references: references ?? this.references,
      amendment: amendment ?? this.amendment,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

import 'package:cloud_firestore/cloud_firestore.dart';

class UserSettingsModel {
  final String uid;
  final String themeMode;
  final String fontSize;
  final String textAlignment;
  final String lineSpacing;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserSettingsModel({
    required this.uid,
    required this.themeMode,
    required this.fontSize,
    required this.textAlignment,
    required this.lineSpacing,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserSettingsModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return UserSettingsModel(
      uid: doc.id,
      themeMode: data['theme_mode'] ?? 'light',
      fontSize: data['font_size'] ?? 'medium',
      textAlignment: data['text_alignment'] ?? 'left',
      lineSpacing: data['line_spacing'] ?? 'medium',
      createdAt: (data['created_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updated_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'uid': uid,
      'theme_mode': themeMode,
      'font_size': fontSize,
      'text_alignment': textAlignment,
      'line_spacing': lineSpacing,
      'created_at': Timestamp.fromDate(createdAt),
      'updated_at': Timestamp.fromDate(updatedAt),
    };
  }

  UserSettingsModel copyWith({
    String? uid,
    String? themeMode,
    String? fontSize,
    String? textAlignment,
    String? lineSpacing,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserSettingsModel(
      uid: uid ?? this.uid,
      themeMode: themeMode ?? this.themeMode,
      fontSize: fontSize ?? this.fontSize,
      textAlignment: textAlignment ?? this.textAlignment,
      lineSpacing: lineSpacing ?? this.lineSpacing,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

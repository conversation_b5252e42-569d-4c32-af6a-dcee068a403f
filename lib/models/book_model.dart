import 'package:cloud_firestore/cloud_firestore.dart';

class BookModel {
  final String id;
  final String title;
  final int totalSections;
  final String category;
  final int publishedYear;
  final String jurisdiction;
  final bool isNew;
  final DateTime createdAt;
  final DateTime updatedAt;

  BookModel({
    required this.id,
    required this.title,
    required this.totalSections,
    required this.category,
    required this.publishedYear,
    required this.jurisdiction,
    required this.isNew,
    required this.createdAt,
    required this.updatedAt,
  });

  factory BookModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return BookModel(
      id: doc.id,
      title: data['title'] ?? '',
      totalSections: data['total_sections'] ?? 0,
      category: data['category'] ?? '',
      publishedYear: data['published_year'] ?? DateTime.now().year,
      jurisdiction: data['jurisdiction'] ?? '',
      isNew: data['is_new'] ?? false,
      createdAt: (data['created_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updated_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'total_sections': totalSections,
      'category': category,
      'published_year': publishedYear,
      'jurisdiction': jurisdiction,
      'is_new': isNew,
      'created_at': Timestamp.fromDate(createdAt),
      'updated_at': Timestamp.fromDate(updatedAt),
    };
  }

  BookModel copyWith({
    String? id,
    String? title,
    int? totalSections,
    String? category,
    int? publishedYear,
    String? jurisdiction,
    bool? isNew,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BookModel(
      id: id ?? this.id,
      title: title ?? this.title,
      totalSections: totalSections ?? this.totalSections,
      category: category ?? this.category,
      publishedYear: publishedYear ?? this.publishedYear,
      jurisdiction: jurisdiction ?? this.jurisdiction,
      isNew: isNew ?? this.isNew,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

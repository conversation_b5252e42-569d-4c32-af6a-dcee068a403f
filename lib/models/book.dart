class Book {
  final String id;
  final String title;
  final String description;
  final int totalSections;
  final List<String> tags;
  bool isPinned;
  final bool isNew;
  final double progress;
  final DateTime? lastReadAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  Book({
    required this.id,
    required this.title,
    required this.description,
    required this.totalSections,
    required this.tags,
    this.isPinned = false,
    this.isNew = false,
    this.progress = 0.0,
    this.lastReadAt,
    required this.createdAt,
    required this.updatedAt,
  });

  Book copyWith({
    String? id,
    String? title,
    String? description,
    int? totalSections,
    List<String>? tags,
    bool? isPinned,
    bool? isNew,
    double? progress,
    DateTime? lastReadAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Book(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      totalSections: totalSections ?? this.totalSections,
      tags: tags ?? this.tags,
      isPinned: isPinned ?? this.isPinned,
      isNew: isNew ?? this.isNew,
      progress: progress ?? this.progress,
      lastReadAt: lastReadAt ?? this.lastReadAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

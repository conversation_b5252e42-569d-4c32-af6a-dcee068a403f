import 'package:cloud_firestore/cloud_firestore.dart';

class UserBookModel {
  final String id;
  final String uid;
  final String bookId;
  final bool isPinned;
  final double progress;
  final int lastReadSectionOrder;
  final DateTime lastAccessedAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserBookModel({
    required this.id,
    required this.uid,
    required this.bookId,
    required this.isPinned,
    required this.progress,
    required this.lastReadSectionOrder,
    required this.lastAccessedAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserBookModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return UserBookModel(
      id: doc.id,
      uid: data['uid'] ?? '',
      bookId: data['book_id'] ?? '',
      isPinned: data['is_pinned'] ?? false,
      progress: (data['progress'] ?? 0.0).toDouble(),
      lastReadSectionOrder: data['last_read_section_order'] ?? 0,
      lastAccessedAt: (data['last_accessed_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
      createdAt: (data['created_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updated_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'uid': uid,
      'book_id': bookId,
      'is_pinned': isPinned,
      'progress': progress,
      'last_read_section_order': lastReadSectionOrder,
      'last_accessed_at': Timestamp.fromDate(lastAccessedAt),
      'created_at': Timestamp.fromDate(createdAt),
      'updated_at': Timestamp.fromDate(updatedAt),
    };
  }

  UserBookModel copyWith({
    String? id,
    String? uid,
    String? bookId,
    bool? isPinned,
    double? progress,
    int? lastReadSectionOrder,
    DateTime? lastAccessedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserBookModel(
      id: id ?? this.id,
      uid: uid ?? this.uid,
      bookId: bookId ?? this.bookId,
      isPinned: isPinned ?? this.isPinned,
      progress: progress ?? this.progress,
      lastReadSectionOrder: lastReadSectionOrder ?? this.lastReadSectionOrder,
      lastAccessedAt: lastAccessedAt ?? this.lastAccessedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

import 'package:cloud_firestore/cloud_firestore.dart';

class UserFavoriteModel {
  final String id;
  final String uid;
  final String sectionId;
  final DateTime createdAt;

  UserFavoriteModel({
    required this.id,
    required this.uid,
    required this.sectionId,
    required this.createdAt,
  });

  factory UserFavoriteModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return UserFavoriteModel(
      id: doc.id,
      uid: data['uid'] ?? '',
      sectionId: data['section_id'] ?? '',
      createdAt: (data['created_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'uid': uid,
      'section_id': sectionId,
      'created_at': Timestamp.fromDate(createdAt),
    };
  }

  UserFavoriteModel copyWith({
    String? id,
    String? uid,
    String? sectionId,
    DateTime? createdAt,
  }) {
    return UserFavoriteModel(
      id: id ?? this.id,
      uid: uid ?? this.uid,
      sectionId: sectionId ?? this.sectionId,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

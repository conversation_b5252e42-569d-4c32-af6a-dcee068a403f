import 'package:cloud_firestore/cloud_firestore.dart';

class SubscriptionModel {
  final String uid;
  final String status; // 'free_trial', 'active', 'expired', 'cancelled'
  final DateTime? trialStart;
  final DateTime? trialEnd;
  final String? subscriptionPlan; // '3_months', '6_months', '12_months'
  final DateTime? subscriptionStart;
  final DateTime? subscriptionEnd;
  final String? paymentMethod;
  final String? razorpayPaymentId;
  final DateTime createdAt;
  final DateTime updatedAt;

  SubscriptionModel({
    required this.uid,
    required this.status,
    this.trialStart,
    this.trialEnd,
    this.subscriptionPlan,
    this.subscriptionStart,
    this.subscriptionEnd,
    this.paymentMethod,
    this.razorpayPaymentId,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SubscriptionModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return SubscriptionModel(
      uid: doc.id,
      status: data['status'] ?? 'free_trial',
      trialStart: (data['trial_start'] as Timestamp?)?.toDate(),
      trialEnd: (data['trial_end'] as Timestamp?)?.toDate(),
      subscriptionPlan: data['subscription_plan'],
      subscriptionStart: (data['subscription_start'] as Timestamp?)?.toDate(),
      subscriptionEnd: (data['subscription_end'] as Timestamp?)?.toDate(),
      paymentMethod: data['payment_method'],
      razorpayPaymentId: data['razorpay_payment_id'],
      createdAt: (data['created_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updated_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'uid': uid,
      'status': status,
      'trial_start': trialStart != null ? Timestamp.fromDate(trialStart!) : null,
      'trial_end': trialEnd != null ? Timestamp.fromDate(trialEnd!) : null,
      'subscription_plan': subscriptionPlan,
      'subscription_start': subscriptionStart != null ? Timestamp.fromDate(subscriptionStart!) : null,
      'subscription_end': subscriptionEnd != null ? Timestamp.fromDate(subscriptionEnd!) : null,
      'payment_method': paymentMethod,
      'razorpay_payment_id': razorpayPaymentId,
      'created_at': Timestamp.fromDate(createdAt),
      'updated_at': Timestamp.fromDate(updatedAt),
    };
  }

  bool get isActive {
    final now = DateTime.now();
    
    if (status == 'active' && subscriptionEnd != null) {
      return now.isBefore(subscriptionEnd!);
    }
    
    if (status == 'free_trial' && trialEnd != null) {
      return now.isBefore(trialEnd!);
    }
    
    return false;
  }

  int get daysRemaining {
    final now = DateTime.now();
    
    if (status == 'active' && subscriptionEnd != null) {
      return subscriptionEnd!.difference(now).inDays;
    }
    
    if (status == 'free_trial' && trialEnd != null) {
      return trialEnd!.difference(now).inDays;
    }
    
    return 0;
  }

  SubscriptionModel copyWith({
    String? uid,
    String? status,
    DateTime? trialStart,
    DateTime? trialEnd,
    String? subscriptionPlan,
    DateTime? subscriptionStart,
    DateTime? subscriptionEnd,
    String? paymentMethod,
    String? razorpayPaymentId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SubscriptionModel(
      uid: uid ?? this.uid,
      status: status ?? this.status,
      trialStart: trialStart ?? this.trialStart,
      trialEnd: trialEnd ?? this.trialEnd,
      subscriptionPlan: subscriptionPlan ?? this.subscriptionPlan,
      subscriptionStart: subscriptionStart ?? this.subscriptionStart,
      subscriptionEnd: subscriptionEnd ?? this.subscriptionEnd,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      razorpayPaymentId: razorpayPaymentId ?? this.razorpayPaymentId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

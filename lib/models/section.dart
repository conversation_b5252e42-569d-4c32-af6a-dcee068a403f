class Section {
  final String id;
  final String bookId;
  final String title;
  final String content;
  final int sectionNumber;
  bool isBookmarked;
  final DateTime createdAt;

  Section({
    required this.id,
    required this.bookId,
    required this.title,
    required this.content,
    required this.sectionNumber,
    this.isBookmarked = false,
    required this.createdAt,
  });

  Section copyWith({
    String? id,
    String? bookId,
    String? title,
    String? content,
    int? sectionNumber,
    bool? isBookmarked,
    DateTime? createdAt,
  }) {
    return Section(
      id: id ?? this.id,
      bookId: bookId ?? this.bookId,
      title: title ?? this.title,
      content: content ?? this.content,
      sectionNumber: sectionNumber ?? this.sectionNumber,
      isBookmarked: isBookmarked ?? this.isBookmarked,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

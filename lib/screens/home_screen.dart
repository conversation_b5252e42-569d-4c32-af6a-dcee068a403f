import 'package:flutter/material.dart';
// Removed animated_bottom_navigation_bar import
import '../constants/app_constants.dart';
import '../widgets/custom_app_bar.dart';
import 'tabs/home_tab.dart';
import 'tabs/favorites_tab.dart';
import 'tabs/tags_tab.dart';
import 'tabs/books_library_tab.dart';
import 'tabs/profile_tab.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 2; // Start with Home tab (index 2)

  final List<IconData> _iconList = [
    Icons.favorite_outline,
    Icons.local_offer_outlined,
    Icons.home_outlined,
    Icons.library_books_outlined,
    Icons.person_outline,
  ];

  final List<IconData> _activeIconList = [
    Icons.favorite,
    Icons.local_offer,
    Icons.home,
    Icons.library_books,
    Icons.person,
  ];

  final List<String> _labelList = [
    'Favorites',
    'Tags',
    'Home',
    'Books Library',
    'Profile',
  ];

  final List<Widget> _pages = [
    const FavoritesTab(),
    const TagsTab(),
    const HomeTab(),
    const BooksLibraryTab(),
    const ProfileTab(),
  ];

  // Removed animation controllers since we're using standard BottomNavigationBar

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: _labelList[_currentIndex],
        showSearch: _currentIndex == 2 || _currentIndex == 3, // Show search on Home and Books Library
        showNotifications: true,
      ),
      body: IndexedStack(
        index: _currentIndex,
        children: _pages,
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(32),
            topRight: Radius.circular(32),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(32),
            topRight: Radius.circular(32),
          ),
          child: BottomNavigationBar(
            type: BottomNavigationBarType.fixed,
            currentIndex: _currentIndex,
            onTap: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            backgroundColor: Theme.of(context).scaffoldBackgroundColor,
            selectedItemColor: AppConstants.primaryColor,
            unselectedItemColor: AppConstants.textSecondaryColor,
            selectedFontSize: 12,
            unselectedFontSize: 12,
            elevation: 0,
            items: List.generate(_iconList.length, (index) {
              return BottomNavigationBarItem(
                icon: Icon(
                  _currentIndex == index ? _activeIconList[index] : _iconList[index],
                  size: 24,
                ),
                label: _labelList[index],
              );
            }),
          ),
        ),
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search'),
        content: const Text('Search functionality will be implemented in the next phase.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

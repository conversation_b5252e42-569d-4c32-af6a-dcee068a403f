import 'package:flutter/material.dart';
import 'package:animated_bottom_navigation_bar/animated_bottom_navigation_bar.dart';
import '../constants/app_constants.dart';
import '../widgets/custom_app_bar.dart';
import 'tabs/home_tab.dart';
import 'tabs/favorites_tab.dart';
import 'tabs/tags_tab.dart';
import 'tabs/books_library_tab.dart';
import 'tabs/profile_tab.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  int _currentIndex = 2; // Start with Home tab (index 2)
  late AnimationController _animationController;
  late Animation<double> _animation;
  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;

  final List<IconData> _iconList = [
    Icons.favorite_outline,
    Icons.local_offer_outlined,
    Icons.home_outlined,
    Icons.library_books_outlined,
    Icons.person_outline,
  ];

  final List<IconData> _activeIconList = [
    Icons.favorite,
    Icons.local_offer,
    Icons.home,
    Icons.library_books,
    Icons.person,
  ];

  final List<String> _labelList = [
    'Favorites',
    'Tags',
    'Home',
    'Books Library',
    'Profile',
  ];

  final List<Widget> _pages = [
    const FavoritesTab(),
    const TagsTab(),
    const HomeTab(),
    const BooksLibraryTab(),
    const ProfileTab(),
  ];

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fabAnimation = CurvedAnimation(
      parent: _fabAnimationController,
      curve: Curves.easeInOut,
    );

    _animationController.forward();
    _fabAnimationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: _labelList[_currentIndex],
        showSearch: _currentIndex == 2 || _currentIndex == 3, // Show search on Home and Books Library
        showNotifications: true,
      ),
      body: IndexedStack(
        index: _currentIndex,
        children: _pages,
      ),
      floatingActionButton: _currentIndex == 2 // Only show FAB on Home tab
          ? ScaleTransition(
              scale: _fabAnimation,
              child: FloatingActionButton(
                onPressed: () {
                  // Navigate to search or add new book functionality
                  _showSearchDialog();
                },
                backgroundColor: AppConstants.primaryColor,
                child: const Icon(Icons.search, color: Colors.white),
              ),
            )
          : null,
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      bottomNavigationBar: AnimatedBottomNavigationBar.builder(
        itemCount: _iconList.length,
        tabBuilder: (int index, bool isActive) {
          final color = isActive
              ? AppConstants.primaryColor
              : AppConstants.textSecondaryColor;

          return Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                isActive ? _activeIconList[index] : _iconList[index],
                size: 24,
                color: color,
              ),
              const SizedBox(height: 4),
              Text(
                _labelList[index],
                style: TextStyle(
                  color: color,
                  fontSize: 12,
                  fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ],
          );
        },
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        activeIndex: _currentIndex,
        splashColor: AppConstants.secondaryColor,
        notchAndCornersAnimation: _animation,
        splashSpeedInMilliseconds: 300,
        notchSmoothness: NotchSmoothness.defaultEdge,
        gapLocation: _currentIndex == 2 ? GapLocation.center : GapLocation.none,
        leftCornerRadius: 32,
        rightCornerRadius: 32,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });

          // Animate FAB based on selected tab
          if (index == 2) {
            _fabAnimationController.forward();
          } else {
            _fabAnimationController.reverse();
          }
        },
        height: 80,
        elevation: 8,
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search'),
        content: const Text('Search functionality will be implemented in the next phase.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

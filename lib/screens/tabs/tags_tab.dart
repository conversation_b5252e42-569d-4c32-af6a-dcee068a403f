import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../../widgets/tag_card.dart';
import '../../widgets/section_card.dart';
import '../../models/tag.dart';
import '../../models/book.dart';
import '../../services/sample_data_service.dart';
import '../book_details_screen.dart';

class TagsTab extends StatefulWidget {
  const TagsTab({super.key});

  @override
  State<TagsTab> createState() => _TagsTabState();
}

class _TagsTabState extends State<TagsTab> {
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _newTagController = TextEditingController();
  String _searchQuery = '';
  String? _selectedTag;

  List<Tag> _tags = [];
  List<Book> _tagBooks = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadTags();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _newTagController.dispose();
    super.dispose();
  }

  void _loadTags() {
    setState(() {
      _isLoading = true;
    });

    // Simulate loading delay
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _tags = SampleDataService.getSampleTags();
          _isLoading = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Search and Add Tag Bar
        _buildSearchAndAddBar(),
        
        // Content Area
        Expanded(
          child: _selectedTag == null
              ? _buildTagsList()
              : _buildTagSections(),
        ),
      ],
    );
  }

  Widget _buildSearchAndAddBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      color: Colors.white,
      child: Column(
        children: [
          // Search Bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search tags...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        setState(() {
                          _searchController.clear();
                          _searchQuery = '';
                        });
                      },
                    )
                  : null,
              filled: true,
              fillColor: Colors.grey[100],
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                borderSide: BorderSide.none,
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: AppConstants.defaultPadding,
                vertical: 12,
              ),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          
          const SizedBox(height: 16),
          
          // Add New Tag Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _showAddTagDialog,
              icon: const Icon(Icons.add),
              label: const Text('Create New Tag'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTagsList() {
    // This would be replaced with actual data from Firestore
    final userTags = [
      {'name': 'Important', 'color': Colors.red, 'sectionsCount': 15},
      {'name': 'Criminal Law', 'color': Colors.blue, 'sectionsCount': 8},
      {'name': 'Constitutional', 'color': Colors.green, 'sectionsCount': 12},
      {'name': 'Civil Rights', 'color': Colors.orange, 'sectionsCount': 6},
      {'name': 'Property Law', 'color': Colors.purple, 'sectionsCount': 4},
      {'name': 'Family Law', 'color': Colors.teal, 'sectionsCount': 7},
      {'name': 'Labor Law', 'color': Colors.indigo, 'sectionsCount': 3},
      {'name': 'Tax Law', 'color': Colors.brown, 'sectionsCount': 5},
    ];
    
    final filteredTags = userTags.where((tag) {
      if (_searchQuery.isEmpty) return true;
      return (tag['name'] as String).toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
    
    if (filteredTags.isEmpty && _searchQuery.isNotEmpty) {
      return _buildNoSearchResults();
    }
    
    if (userTags.isEmpty) {
      return _buildEmptyState();
    }
    
    return RefreshIndicator(
      onRefresh: _refreshTags,
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: filteredTags.length,
        itemBuilder: (context, index) {
          final tag = filteredTags[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: TagCard(
              name: tag['name'] as String,
              color: tag['color'] as Color,
              sectionsCount: tag['sectionsCount'] as int,
              onTap: () {
                setState(() {
                  _selectedTag = tag['name'] as String;
                });
              },
              onEdit: () {
                _showEditTagDialog(tag);
              },
              onDelete: () {
                _showDeleteTagDialog(tag);
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildTagSections() {
    // This would be replaced with actual sections data for the selected tag
    final tagSections = List.generate(
      5,
      (index) => {
        'sectionTitle': 'Section ${index + 1}: $_selectedTag Related Content',
        'bookTitle': 'Legal Book ${index + 1}',
        'content': 'This section contains content related to $_selectedTag and explains important legal provisions...',
        'tags': [_selectedTag!, 'Legal'],
      },
    );
    
    return Column(
      children: [
        // Header with back button and tag info
        Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          color: AppConstants.secondaryColor,
          child: Row(
            children: [
              IconButton(
                onPressed: () {
                  setState(() {
                    _selectedTag = null;
                  });
                },
                icon: const Icon(Icons.arrow_back),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _selectedTag!,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppConstants.textPrimaryColor,
                      ),
                    ),
                    Text(
                      '${tagSections.length} sections',
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppConstants.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),
              IconButton(
                onPressed: () {
                  // Edit tag
                },
                icon: const Icon(Icons.edit),
              ),
            ],
          ),
        ),
        
        // Sections List
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            itemCount: tagSections.length,
            itemBuilder: (context, index) {
              final section = tagSections[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: SectionCard(
                  sectionTitle: section['sectionTitle'] as String,
                  bookTitle: section['bookTitle'] as String,
                  content: section['content'] as String,
                  isFavorite: false,
                  tags: List<String>.from(section['tags'] as List),
                  onTap: () {
                    // Navigate to section details
                  },
                  onFavoriteToggle: () {
                    // Toggle favorite
                  },
                  onTagTap: (tag) {
                    // Navigate to tag or filter
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.local_offer_outlined,
            size: 80,
            color: AppConstants.textSecondaryColor,
          ),
          const SizedBox(height: 24),
          const Text(
            'No Tags Created',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 12),
          const Text(
            'Create tags to organize your\nfavorite sections',
            style: TextStyle(
              fontSize: 16,
              color: AppConstants.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: _showAddTagDialog,
            icon: const Icon(Icons.add),
            label: const Text('Create First Tag'),
          ),
        ],
      ),
    );
  }

  Widget _buildNoSearchResults() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.search_off,
            size: 64,
            color: AppConstants.textSecondaryColor,
          ),
          const SizedBox(height: 16),
          const Text(
            'No tags found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'No tags match "$_searchQuery"',
            style: const TextStyle(
              fontSize: 14,
              color: AppConstants.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  void _showAddTagDialog() {
    _newTagController.clear();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create New Tag'),
        content: TextField(
          controller: _newTagController,
          decoration: const InputDecoration(
            labelText: 'Tag Name',
            hintText: 'Enter tag name',
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (_newTagController.text.isNotEmpty) {
                Navigator.of(context).pop();
                _createTag(_newTagController.text);
              }
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }

  void _showEditTagDialog(Map<String, dynamic> tag) {
    _newTagController.text = tag['name'] as String;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Tag'),
        content: TextField(
          controller: _newTagController,
          decoration: const InputDecoration(
            labelText: 'Tag Name',
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (_newTagController.text.isNotEmpty) {
                Navigator.of(context).pop();
                _updateTag(tag, _newTagController.text);
              }
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  void _showDeleteTagDialog(Map<String, dynamic> tag) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Tag'),
        content: Text('Delete "${tag['name']}" tag? This will remove the tag from all sections.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteTag(tag);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.errorColor,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _createTag(String tagName) {
    // Create tag in Firestore
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Tag "$tagName" created'),
        backgroundColor: AppConstants.successColor,
      ),
    );
  }

  void _updateTag(Map<String, dynamic> tag, String newName) {
    // Update tag in Firestore
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Tag updated to "$newName"'),
        backgroundColor: AppConstants.successColor,
      ),
    );
  }

  void _deleteTag(Map<String, dynamic> tag) {
    // Delete tag from Firestore
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Tag "${tag['name']}" deleted'),
        backgroundColor: AppConstants.successColor,
      ),
    );
  }

  Future<void> _refreshTags() async {
    // Refresh tags from Firestore
    await Future.delayed(const Duration(seconds: 1));
    if (mounted) {
      setState(() {
        // Update tags data
      });
    }
  }
}

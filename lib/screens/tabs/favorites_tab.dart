import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../../widgets/section_card.dart';
import '../../widgets/book_card.dart';
import '../../models/book.dart';
import '../../models/section.dart';
import '../../services/sample_data_service.dart';
import '../book_details_screen.dart';

class FavoritesTab extends StatefulWidget {
  const FavoritesTab({super.key});

  @override
  State<FavoritesTab> createState() => _FavoritesTabState();
}

class _FavoritesTabState extends State<FavoritesTab> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedSort = 'Latest';
  String _selectedTab = 'Books'; // Books or Sections

  List<Book> _favoriteBooks = [];
  List<Section> _favoriteSections = [];
  bool _isLoading = true;

  final List<String> _sortOptions = [
    'Latest',
    'Oldest',
    'Book Name',
    'Section Name',
  ];

  @override
  void initState() {
    super.initState();
    _loadFavorites();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadFavorites() {
    setState(() {
      _isLoading = true;
    });

    // Simulate loading delay
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        final allBooks = SampleDataService.getSampleBooks();
        final allSections = <Section>[];

        // Get all sections from all books
        for (final book in allBooks) {
          allSections.addAll(SampleDataService.getSampleSections(book.id));
        }

        setState(() {
          _favoriteBooks = allBooks.where((book) => book.isPinned).toList();
          _favoriteSections = allSections.where((section) => section.isBookmarked).toList();
          _isLoading = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Tab Selector
        _buildTabSelector(),

        // Search and Sort Bar
        _buildSearchAndSortBar(),

        // Favorites List
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _buildFavoritesList(),
        ),
      ],
    );
  }

  Widget _buildTabSelector() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      color: Colors.white,
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () => setState(() => _selectedTab = 'Books'),
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        decoration: BoxDecoration(
                          color: _selectedTab == 'Books'
                              ? AppConstants.primaryColor
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'Favorite Books (${_favoriteBooks.length})',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: _selectedTab == 'Books'
                                ? Colors.white
                                : AppConstants.textPrimaryColor,
                          ),
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: GestureDetector(
                      onTap: () => setState(() => _selectedTab = 'Sections'),
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        decoration: BoxDecoration(
                          color: _selectedTab == 'Sections'
                              ? AppConstants.primaryColor
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'Bookmarked Sections (${_favoriteSections.length})',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: _selectedTab == 'Sections'
                                ? Colors.white
                                : AppConstants.textPrimaryColor,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndSortBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      color: Colors.white,
      child: Column(
        children: [
          // Search Bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search favorites...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        setState(() {
                          _searchController.clear();
                          _searchQuery = '';
                        });
                      },
                    )
                  : null,
              filled: true,
              fillColor: Colors.grey[100],
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                borderSide: BorderSide.none,
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: AppConstants.defaultPadding,
                vertical: 12,
              ),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          
          const SizedBox(height: 16),
          
          // Sort Options
          Row(
            children: [
              const Text(
                'Sort by:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppConstants.textPrimaryColor,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedSort,
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                    ),
                  ),
                  items: _sortOptions.map((option) {
                    return DropdownMenuItem<String>(
                      value: option,
                      child: Text(option),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedSort = value;
                      });
                    }
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFavoritesList() {
    if (_selectedTab == 'Books') {
      return _buildFavoriteBooksGrid();
    } else {
      return _buildFavoriteSectionsList();
    }
  }

  Widget _buildFavoriteBooksGrid() {
    final filteredBooks = _favoriteBooks.where((book) {
      if (_searchQuery.isEmpty) return true;
      return book.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             book.description.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();

    if (filteredBooks.isEmpty) {
      return _buildEmptyState('No favorite books found');
    }

    return GridView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: filteredBooks.length,
      itemBuilder: (context, index) {
        final book = filteredBooks[index];
        return BookCard(
          title: book.title,
          totalSections: book.totalSections,
          progress: book.progress,
          isPinned: book.isPinned,
          isNew: book.isNew,
          onTap: () => _navigateToBookDetails(book),
          onPinToggle: () => _toggleBookPin(book),
        );
      },
    );
  }

  Widget _buildFavoriteSectionsList() {
    final filteredSections = _favoriteSections.where((section) {
      if (_searchQuery.isEmpty) return true;
      return section.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             section.content.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();

    if (filteredSections.isEmpty) {
      return _buildEmptyState('No bookmarked sections found');
    }

    return RefreshIndicator(
      onRefresh: _refreshFavorites,
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: filteredSections.length,
        itemBuilder: (context, index) {
          final section = filteredSections[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: SectionCard(
              sectionTitle: section.title,
              bookTitle: 'Book ${section.bookId}', // You could get actual book title
              content: section.content,
              isFavorite: section.isBookmarked,
              tags: const ['Legal', 'Important'], // Could be dynamic
              onTap: () => _navigateToSection(section),
              onFavoriteToggle: () => _toggleSectionBookmark(section),
              onTagTap: (tag) => _filterByTag(tag),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _selectedTab == 'Books' ? Icons.book_outlined : Icons.bookmark_border,
            size: 80,
            color: AppConstants.textSecondaryColor,
          ),
          const SizedBox(height: 24),
          Text(
            _selectedTab == 'Books' ? 'No Favorite Books' : 'No Bookmarked Sections',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            message,
            style: const TextStyle(
              fontSize: 16,
              color: AppConstants.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: () {
              // Navigate to books library
            },
            icon: const Icon(Icons.library_books),
            label: const Text('Browse Books'),
          ),
        ],
      ),
    );
  }

  Widget _buildNoSearchResults() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.search_off,
            size: 64,
            color: AppConstants.textSecondaryColor,
          ),
          const SizedBox(height: 16),
          const Text(
            'No favorites found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'No favorites match "$_searchQuery"',
            style: const TextStyle(
              fontSize: 14,
              color: AppConstants.textSecondaryColor,
            ),
          ),
          const SizedBox(height: 16),
          TextButton(
            onPressed: () {
              setState(() {
                _searchController.clear();
                _searchQuery = '';
              });
            },
            child: const Text('Clear Search'),
          ),
        ],
      ),
    );
  }

  Future<void> _refreshFavorites() async {
    // Simulate refresh delay
    await Future.delayed(const Duration(seconds: 1));
    
    // Refresh favorites from Firestore
    if (mounted) {
      setState(() {
        // Update favorites data
      });
    }
  }

  void _navigateToSection(Map<String, dynamic> section) {
    // Navigate to section details page
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(section['sectionTitle'] as String),
        content: Text(section['content'] as String),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Navigate to full section page
            },
            child: const Text('Read Full'),
          ),
        ],
      ),
    );
  }

  void _removeFavorite(Map<String, dynamic> section) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove Favorite'),
        content: Text('Remove "${section['sectionTitle']}" from favorites?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Remove from favorites in Firestore
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Removed from favorites'),
                  backgroundColor: AppConstants.successColor,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.errorColor,
            ),
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }

  Future<void> _refreshFavorites() async {
    _loadFavorites();
  }

  void _navigateToBookDetails(Book book) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => BookDetailsScreen(book: book),
      ),
    );
  }

  void _toggleBookPin(Book book) {
    setState(() {
      book.isPinned = !book.isPinned;
      if (!book.isPinned) {
        _favoriteBooks.remove(book);
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          book.isPinned ? 'Book added to favorites!' : 'Book removed from favorites!',
        ),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _navigateToSection(Section section) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening ${section.title}...'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _toggleSectionBookmark(Section section) {
    setState(() {
      section.isBookmarked = !section.isBookmarked;
      if (!section.isBookmarked) {
        _favoriteSections.remove(section);
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          section.isBookmarked ? 'Section bookmarked!' : 'Bookmark removed!',
        ),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _filterByTag(String tag) {
    // Implement tag filtering
    setState(() {
      _searchController.text = tag;
      _searchQuery = tag;
    });
  }
}

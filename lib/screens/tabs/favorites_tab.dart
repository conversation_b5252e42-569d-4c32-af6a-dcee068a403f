import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../../widgets/section_card.dart';

class FavoritesTab extends StatefulWidget {
  const FavoritesTab({super.key});

  @override
  State<FavoritesTab> createState() => _FavoritesTabState();
}

class _FavoritesTabState extends State<FavoritesTab> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedSort = 'Latest';
  
  final List<String> _sortOptions = [
    'Latest',
    'Oldest',
    'Book Name',
    'Section Name',
  ];
  
  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Search and Sort Bar
        _buildSearchAndSortBar(),
        
        // Favorites List
        Expanded(
          child: _buildFavoritesList(),
        ),
      ],
    );
  }

  Widget _buildSearchAndSortBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      color: Colors.white,
      child: Column(
        children: [
          // Search Bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search favorites...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        setState(() {
                          _searchController.clear();
                          _searchQuery = '';
                        });
                      },
                    )
                  : null,
              filled: true,
              fillColor: Colors.grey[100],
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                borderSide: BorderSide.none,
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: AppConstants.defaultPadding,
                vertical: 12,
              ),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          
          const SizedBox(height: 16),
          
          // Sort Options
          Row(
            children: [
              const Text(
                'Sort by:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppConstants.textPrimaryColor,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedSort,
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                    ),
                  ),
                  items: _sortOptions.map((option) {
                    return DropdownMenuItem<String>(
                      value: option,
                      child: Text(option),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedSort = value;
                      });
                    }
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFavoritesList() {
    // This would be replaced with actual data from Firestore
    final favoritesSections = List.generate(
      10,
      (index) => {
        'sectionTitle': 'Section ${index + 1}: Important Legal Provision',
        'bookTitle': 'Indian Penal Code',
        'content': 'This is a preview of the section content that explains important legal provisions and their applications...',
        'dateAdded': DateTime.now().subtract(Duration(days: index)),
        'tags': ['Criminal Law', 'Important'],
      },
    );
    
    if (favoritesSections.isEmpty) {
      return _buildEmptyState();
    }
    
    final filteredSections = favoritesSections.where((section) {
      if (_searchQuery.isEmpty) return true;
      
      final sectionTitle = (section['sectionTitle'] as String).toLowerCase();
      final bookTitle = (section['bookTitle'] as String).toLowerCase();
      final query = _searchQuery.toLowerCase();
      
      return sectionTitle.contains(query) || bookTitle.contains(query);
    }).toList();
    
    if (filteredSections.isEmpty) {
      return _buildNoSearchResults();
    }
    
    return RefreshIndicator(
      onRefresh: _refreshFavorites,
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: filteredSections.length,
        itemBuilder: (context, index) {
          final section = filteredSections[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: SectionCard(
              sectionTitle: section['sectionTitle'] as String,
              bookTitle: section['bookTitle'] as String,
              content: section['content'] as String,
              isFavorite: true,
              tags: List<String>.from(section['tags'] as List),
              onTap: () {
                // Navigate to section details
                _navigateToSection(section);
              },
              onFavoriteToggle: () {
                _removeFavorite(section);
              },
              onTagTap: (tag) {
                // Filter by tag or navigate to tag
                _filterByTag(tag);
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.favorite_border,
            size: 80,
            color: AppConstants.textSecondaryColor,
          ),
          const SizedBox(height: 24),
          const Text(
            'No Favorites Yet',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 12),
          const Text(
            'Start adding sections to your favorites\nfor quick access later',
            style: TextStyle(
              fontSize: 16,
              color: AppConstants.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: () {
              // Navigate to books library
            },
            icon: const Icon(Icons.library_books),
            label: const Text('Browse Books'),
          ),
        ],
      ),
    );
  }

  Widget _buildNoSearchResults() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.search_off,
            size: 64,
            color: AppConstants.textSecondaryColor,
          ),
          const SizedBox(height: 16),
          const Text(
            'No favorites found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'No favorites match "$_searchQuery"',
            style: const TextStyle(
              fontSize: 14,
              color: AppConstants.textSecondaryColor,
            ),
          ),
          const SizedBox(height: 16),
          TextButton(
            onPressed: () {
              setState(() {
                _searchController.clear();
                _searchQuery = '';
              });
            },
            child: const Text('Clear Search'),
          ),
        ],
      ),
    );
  }

  Future<void> _refreshFavorites() async {
    // Simulate refresh delay
    await Future.delayed(const Duration(seconds: 1));
    
    // Refresh favorites from Firestore
    if (mounted) {
      setState(() {
        // Update favorites data
      });
    }
  }

  void _navigateToSection(Map<String, dynamic> section) {
    // Navigate to section details page
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(section['sectionTitle'] as String),
        content: Text(section['content'] as String),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Navigate to full section page
            },
            child: const Text('Read Full'),
          ),
        ],
      ),
    );
  }

  void _removeFavorite(Map<String, dynamic> section) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove Favorite'),
        content: Text('Remove "${section['sectionTitle']}" from favorites?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Remove from favorites in Firestore
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Removed from favorites'),
                  backgroundColor: AppConstants.successColor,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.errorColor,
            ),
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }

  void _filterByTag(String tag) {
    // Implement tag filtering
    setState(() {
      _searchController.text = tag;
      _searchQuery = tag;
    });
  }
}

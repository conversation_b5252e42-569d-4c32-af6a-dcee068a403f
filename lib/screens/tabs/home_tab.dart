import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/book_card.dart';
import '../../widgets/section_divider.dart';

class HomeTab extends StatefulWidget {
  const HomeTab({super.key});

  @override
  State<HomeTab> createState() => _HomeTabState();
}

class _HomeTabState extends State<HomeTab> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _refreshData,
      child: SingleChildScrollView(
        controller: _scrollController,
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome Section
            _buildWelcomeSection(),
            
            const SizedBox(height: 24),
            
            // Pinned Books Section
            _buildPinnedBooksSection(),
            
            const SizedBox(height: 24),
            
            // Recently Accessed Books Section
            _buildRecentlyAccessedSection(),
            
            const SizedBox(height: 24),
            
            // Latest Books Section
            _buildLatestBooksSection(),
            
            const SizedBox(height: 24),
            
            // All Books Library Link
            _buildAllBooksLink(),
            
            const SizedBox(height: 100), // Extra space for FAB
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final userName = authProvider.user?.displayName ?? 'User';
        
        return Container(
          padding: const EdgeInsets.all(AppConstants.largePadding),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppConstants.primaryColor,
                AppConstants.primaryColor.withOpacity(0.8),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(AppConstants.largeBorderRadius),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Welcome back, $userName!',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'Continue your legal research journey',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white70,
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  _buildStatCard('Books Read', '12'),
                  const SizedBox(width: 16),
                  _buildStatCard('Favorites', '8'),
                  const SizedBox(width: 16),
                  _buildStatCard('Tags', '5'),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatCard(String label, String value) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
        child: Column(
          children: [
            Text(
              value,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPinnedBooksSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SectionDivider(
          title: 'Pinned Books',
          actionText: 'View All',
          onActionTap: () {
            // Navigate to pinned books
          },
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.vertical,
            itemCount: 3, // This will be dynamic
            itemBuilder: (context, index) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: BookCard(
                  title: 'Indian Penal Code ${index + 1}',
                  totalSections: 511 + index * 10,
                  progress: 0.3 + (index * 0.2),
                  isPinned: true,
                  isNew: index == 0,
                  onTap: () {
                    // Navigate to book details
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildRecentlyAccessedSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SectionDivider(
          title: 'Recently Accessed',
          actionText: 'View All',
          onActionTap: () {
            // Navigate to recently accessed books
          },
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 180,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: 5, // This will be dynamic
            itemBuilder: (context, index) {
              return Padding(
                padding: const EdgeInsets.only(right: 12),
                child: SizedBox(
                  width: 280,
                  child: BookCard(
                    title: 'Constitution of India Chapter ${index + 1}',
                    totalSections: 395 + index * 5,
                    progress: 0.1 + (index * 0.15),
                    isPinned: false,
                    isNew: false,
                    onTap: () {
                      // Navigate to book details
                    },
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLatestBooksSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SectionDivider(
          title: 'Latest Books',
          actionText: 'View All',
          onActionTap: () {
            // Navigate to latest books
          },
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 180,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: 5, // This will be dynamic
            itemBuilder: (context, index) {
              return Padding(
                padding: const EdgeInsets.only(right: 12),
                child: SizedBox(
                  width: 280,
                  child: BookCard(
                    title: 'New Law Book ${index + 1}',
                    totalSections: 200 + index * 20,
                    progress: 0.0,
                    isPinned: false,
                    isNew: true,
                    onTap: () {
                      // Navigate to book details
                    },
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAllBooksLink() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.largePadding),
      decoration: BoxDecoration(
        border: Border.all(color: AppConstants.primaryColor),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.library_books,
            size: 48,
            color: AppConstants.primaryColor,
          ),
          const SizedBox(height: 12),
          const Text(
            'Explore All Books',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppConstants.primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Browse our complete collection of legal books',
            style: TextStyle(
              fontSize: 14,
              color: AppConstants.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              // Navigate to books library tab
            },
            child: const Text('View Library'),
          ),
        ],
      ),
    );
  }

  Future<void> _refreshData() async {
    // Simulate refresh delay
    await Future.delayed(const Duration(seconds: 1));
    
    // Refresh data from Firestore
    if (mounted) {
      setState(() {
        // Update data
      });
    }
  }
}

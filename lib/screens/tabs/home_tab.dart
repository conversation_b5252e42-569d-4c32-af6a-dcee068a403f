import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/book_card.dart';
import '../../widgets/section_divider.dart';
import '../../services/sample_data_service.dart';
import '../../models/book.dart';

class HomeTab extends StatefulWidget {
  const HomeTab({super.key});

  @override
  State<HomeTab> createState() => _HomeTabState();
}

class _HomeTabState extends State<HomeTab> {
  final ScrollController _scrollController = ScrollController();
  List<Book> _pinnedBooks = [];
  List<Book> _recentBooks = [];
  List<Book> _newBooks = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _loadData() {
    setState(() {
      _isLoading = true;
    });

    // Simulate loading delay
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _pinnedBooks = SampleDataService.getFavoriteBooks();
          _recentBooks = SampleDataService.getRecentBooks();
          _newBooks = SampleDataService.getNewBooks();
          _isLoading = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _refreshData,
      child: SingleChildScrollView(
        controller: _scrollController,
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome Section
            _buildWelcomeSection(),
            
            const SizedBox(height: 24),
            
            // Pinned Books Section
            _buildPinnedBooksSection(),
            
            const SizedBox(height: 24),
            
            // Recently Accessed Books Section
            _buildRecentlyAccessedSection(),
            
            const SizedBox(height: 24),
            
            // Latest Books Section
            _buildLatestBooksSection(),
            
            const SizedBox(height: 24),
            
            // All Books Library Link
            _buildAllBooksLink(),
            
            const SizedBox(height: 100), // Extra space for FAB
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final userName = authProvider.user?.displayName ?? 'User';
        
        return Container(
          padding: const EdgeInsets.all(AppConstants.largePadding),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppConstants.primaryColor,
                AppConstants.primaryColor.withOpacity(0.8),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(AppConstants.largeBorderRadius),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Welcome back, $userName!',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'Continue your legal research journey',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white70,
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  _buildStatCard('Books Read', '${SampleDataService.getSampleBooks().length}'),
                  const SizedBox(width: 16),
                  _buildStatCard('Favorites', '${_pinnedBooks.length}'),
                  const SizedBox(width: 16),
                  _buildStatCard('Tags', '${SampleDataService.getSampleTags().length}'),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatCard(String label, String value) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
        child: Column(
          children: [
            Text(
              value,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPinnedBooksSection() {
    if (_isLoading) {
      return _buildLoadingSection('Pinned Books');
    }

    if (_pinnedBooks.isEmpty) {
      return _buildEmptySection('Pinned Books', 'No pinned books yet');
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SectionDivider(
          title: 'Pinned Books',
          actionText: 'View All',
          onActionTap: () {
            // Navigate to pinned books
          },
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.vertical,
            itemCount: _pinnedBooks.length,
            itemBuilder: (context, index) {
              final book = _pinnedBooks[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: BookCard(
                  title: book.title,
                  totalSections: book.totalSections,
                  progress: book.progress,
                  isPinned: book.isPinned,
                  isNew: book.isNew,
                  onTap: () {
                    _navigateToBookDetails(book);
                  },
                  onPinToggle: () {
                    _toggleBookPin(book);
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildRecentlyAccessedSection() {
    if (_isLoading) {
      return _buildLoadingSection('Recently Accessed');
    }

    if (_recentBooks.isEmpty) {
      return _buildEmptySection('Recently Accessed', 'No recently accessed books');
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SectionDivider(
          title: 'Recently Accessed',
          actionText: 'View All',
          onActionTap: () {
            // Navigate to recently accessed books
          },
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 180,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _recentBooks.length,
            itemBuilder: (context, index) {
              final book = _recentBooks[index];
              return Padding(
                padding: const EdgeInsets.only(right: 12),
                child: SizedBox(
                  width: 280,
                  height: 160,
                  child: BookCard(
                    title: book.title,
                    totalSections: book.totalSections,
                    progress: book.progress,
                    isPinned: book.isPinned,
                    isNew: book.isNew,
                    onTap: () {
                      _navigateToBookDetails(book);
                    },
                    onPinToggle: () {
                      _toggleBookPin(book);
                    },
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLatestBooksSection() {
    if (_isLoading) {
      return _buildLoadingSection('Latest Books');
    }

    if (_newBooks.isEmpty) {
      return _buildEmptySection('Latest Books', 'No new books available');
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SectionDivider(
          title: 'Latest Books',
          actionText: 'View All',
          onActionTap: () {
            // Navigate to latest books
          },
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 180,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _newBooks.length,
            itemBuilder: (context, index) {
              final book = _newBooks[index];
              return Padding(
                padding: const EdgeInsets.only(right: 12),
                child: SizedBox(
                  width: 280,
                  height: 160,
                  child: BookCard(
                    title: book.title,
                    totalSections: book.totalSections,
                    progress: book.progress,
                    isPinned: book.isPinned,
                    isNew: book.isNew,
                    onTap: () {
                      _navigateToBookDetails(book);
                    },
                    onPinToggle: () {
                      _toggleBookPin(book);
                    },
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAllBooksLink() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.largePadding),
      decoration: BoxDecoration(
        border: Border.all(color: AppConstants.primaryColor),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.library_books,
            size: 48,
            color: AppConstants.primaryColor,
          ),
          const SizedBox(height: 12),
          const Text(
            'Explore All Books',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppConstants.primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Browse our complete collection of legal books',
            style: TextStyle(
              fontSize: 14,
              color: AppConstants.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              // Navigate to books library tab
            },
            child: const Text('View Library'),
          ),
        ],
      ),
    );
  }

  Future<void> _refreshData() async {
    // Simulate refresh delay
    await Future.delayed(const Duration(seconds: 1));

    // Reload data
    _loadData();
  }

  Widget _buildLoadingSection(String title) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SectionDivider(
          title: title,
          actionText: '',
          onActionTap: () {},
        ),
        const SizedBox(height: 16),
        const Center(
          child: CircularProgressIndicator(),
        ),
      ],
    );
  }

  Widget _buildEmptySection(String title, String message) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SectionDivider(
          title: title,
          actionText: '',
          onActionTap: () {},
        ),
        const SizedBox(height: 16),
        Center(
          child: Column(
            children: [
              Icon(
                Icons.book_outlined,
                size: 48,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 8),
              Text(
                message,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _navigateToBookDetails(Book book) {
    // TODO: Navigate to book details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening ${book.title}...'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _toggleBookPin(Book book) {
    setState(() {
      book.isPinned = !book.isPinned;
      // Update the lists
      _loadData();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          book.isPinned ? 'Book pinned!' : 'Book unpinned!',
        ),
        duration: const Duration(seconds: 1),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../../widgets/book_card.dart';
import '../../widgets/section_divider.dart';
import '../../services/sample_data_service.dart';
import '../../models/book.dart';

class BooksLibraryTab extends StatefulWidget {
  const BooksLibraryTab({super.key});

  @override
  State<BooksLibraryTab> createState() => _BooksLibraryTabState();
}

class _BooksLibraryTabState extends State<BooksLibraryTab> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedCategory = 'All';
  String _selectedSort = 'Latest';
  List<Book> _allBooks = [];
  List<Book> _filteredBooks = [];
  bool _isLoading = true;
  
  final List<String> _categories = [
    'All',
    'Constitutional',
    'Criminal',
    'Civil',
    'Commercial',
    'Family',
    'Labor',
    'Tax',
    'Environmental',
  ];
  
  final List<String> _sortOptions = [
    'Latest',
    'Oldest',
    'A-Z',
    'Z-A',
  ];
  
  @override
  void initState() {
    super.initState();
    _loadBooks();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadBooks() {
    setState(() {
      _isLoading = true;
    });

    // Simulate loading delay
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _allBooks = SampleDataService.getSampleBooks();
          _filteredBooks = _allBooks;
          _isLoading = false;
        });
      }
    });
  }

  void _filterBooks() {
    setState(() {
      _filteredBooks = _allBooks.where((book) {
        // Search filter
        bool matchesSearch = _searchQuery.isEmpty ||
            book.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            book.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            book.tags.any((tag) => tag.toLowerCase().contains(_searchQuery.toLowerCase()));

        // Category filter
        bool matchesCategory = _selectedCategory == 'All' ||
            book.tags.any((tag) => tag.toLowerCase().contains(_selectedCategory.toLowerCase()));

        return matchesSearch && matchesCategory;
      }).toList();

      // Sort books
      switch (_selectedSort) {
        case 'Latest':
          _filteredBooks.sort((a, b) => b.createdAt.compareTo(a.createdAt));
          break;
        case 'Oldest':
          _filteredBooks.sort((a, b) => a.createdAt.compareTo(b.createdAt));
          break;
        case 'A-Z':
          _filteredBooks.sort((a, b) => a.title.compareTo(b.title));
          break;
        case 'Z-A':
          _filteredBooks.sort((a, b) => b.title.compareTo(a.title));
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Search and Filter Bar
        _buildSearchAndFilterBar(),
        
        // Books List
        Expanded(
          child: _searchQuery.isNotEmpty
              ? _buildSearchResults()
              : _buildCategorizedBooks(),
        ),
      ],
    );
  }

  Widget _buildSearchAndFilterBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      color: Colors.white,
      child: Column(
        children: [
          // Search Bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search books...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        setState(() {
                          _searchController.clear();
                          _searchQuery = '';
                        });
                      },
                    )
                  : null,
              filled: true,
              fillColor: Colors.grey[100],
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                borderSide: BorderSide.none,
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: AppConstants.defaultPadding,
                vertical: 12,
              ),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
              _filterBooks();
            },
          ),
          
          const SizedBox(height: 16),
          
          // Filter and Sort Options
          Row(
            children: [
              // Category Filter
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedCategory,
                  decoration: InputDecoration(
                    labelText: 'Category',
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                    ),
                  ),
                  items: _categories.map((category) {
                    return DropdownMenuItem<String>(
                      value: category,
                      child: Text(category),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedCategory = value;
                      });
                      _filterBooks();
                    }
                  },
                ),
              ),
              
              const SizedBox(width: 12),
              
              // Sort Options
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedSort,
                  decoration: InputDecoration(
                    labelText: 'Sort By',
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                    ),
                  ),
                  items: _sortOptions.map((option) {
                    return DropdownMenuItem<String>(
                      value: option,
                      child: Text(option),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedSort = value;
                      });
                      _filterBooks();
                    }
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_filteredBooks.isEmpty) {
      return _buildEmptyResults();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: _filteredBooks.length,
      itemBuilder: (context, index) {
        final book = _filteredBooks[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: BookCard(
            title: book.title,
            totalSections: book.totalSections,
            progress: book.progress,
            isPinned: book.isPinned,
            isNew: book.isNew,
            onTap: () {
              _navigateToBookDetails(book);
            },
            onPinToggle: () {
              _toggleBookPin(book);
            },
          ),
        );
      },
    );
  }

  Widget _buildCategorizedBooks() {
    return ListView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      children: [
        // Constitutional Law Books
        SectionDivider(
          title: 'Constitutional Law',
          actionText: 'View All',
          onActionTap: () {
            // Navigate to constitutional law books
          },
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: 3, // This will be dynamic
          itemBuilder: (context, index) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: BookCard(
                title: 'Constitution of India ${index + 1}',
                totalSections: 395 + index * 5,
                progress: 0.1 + (index * 0.15),
                isPinned: index == 0,
                isNew: false,
                onTap: () {
                  // Navigate to book details
                },
              ),
            );
          },
        ),
        
        const SizedBox(height: 24),
        
        // Criminal Law Books
        SectionDivider(
          title: 'Criminal Law',
          actionText: 'View All',
          onActionTap: () {
            // Navigate to criminal law books
          },
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: 3, // This will be dynamic
          itemBuilder: (context, index) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: BookCard(
                title: 'Indian Penal Code ${index + 1}',
                totalSections: 511 + index * 10,
                progress: 0.3 + (index * 0.2),
                isPinned: index == 1,
                isNew: index == 0,
                onTap: () {
                  // Navigate to book details
                },
              ),
            );
          },
        ),
        
        const SizedBox(height: 24),
        
        // Civil Law Books
        SectionDivider(
          title: 'Civil Law',
          actionText: 'View All',
          onActionTap: () {
            // Navigate to civil law books
          },
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: 3, // This will be dynamic
          itemBuilder: (context, index) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: BookCard(
                title: 'Civil Procedure Code ${index + 1}',
                totalSections: 158 + index * 15,
                progress: 0.2 + (index * 0.1),
                isPinned: index == 2,
                isNew: false,
                onTap: () {
                  // Navigate to book details
                },
              ),
            );
          },
        ),
        
        const SizedBox(height: 100), // Extra space for bottom navigation
      ],
    );
  }

  Widget _buildEmptyResults() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: AppConstants.textSecondaryColor,
          ),
          SizedBox(height: 16),
          Text(
            'No books found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Try a different search term or category',
            style: TextStyle(
              fontSize: 14,
              color: AppConstants.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToBookDetails(Book book) {
    // TODO: Navigate to book details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening ${book.title}...'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _toggleBookPin(Book book) {
    setState(() {
      book.isPinned = !book.isPinned;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          book.isPinned ? 'Book pinned!' : 'Book unpinned!',
        ),
        duration: const Duration(seconds: 1),
      ),
    );
  }
}

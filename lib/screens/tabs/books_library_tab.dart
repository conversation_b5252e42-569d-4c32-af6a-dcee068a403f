import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../../widgets/book_card.dart';
import '../../widgets/section_divider.dart';

class BooksLibraryTab extends StatefulWidget {
  const BooksLibraryTab({super.key});

  @override
  State<BooksLibraryTab> createState() => _BooksLibraryTabState();
}

class _BooksLibraryTabState extends State<BooksLibraryTab> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedCategory = 'All';
  String _selectedSort = 'Latest';
  
  final List<String> _categories = [
    'All',
    'Constitutional',
    'Criminal',
    'Civil',
    'Commercial',
    'Family',
    'Labor',
    'Tax',
    'Environmental',
  ];
  
  final List<String> _sortOptions = [
    'Latest',
    'Oldest',
    'A-Z',
    'Z-A',
  ];
  
  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Search and Filter Bar
        _buildSearchAndFilterBar(),
        
        // Books List
        Expanded(
          child: _searchQuery.isNotEmpty
              ? _buildSearchResults()
              : _buildCategorizedBooks(),
        ),
      ],
    );
  }

  Widget _buildSearchAndFilterBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      color: Colors.white,
      child: Column(
        children: [
          // Search Bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search books...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        setState(() {
                          _searchController.clear();
                          _searchQuery = '';
                        });
                      },
                    )
                  : null,
              filled: true,
              fillColor: Colors.grey[100],
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                borderSide: BorderSide.none,
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: AppConstants.defaultPadding,
                vertical: 12,
              ),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          
          const SizedBox(height: 16),
          
          // Filter and Sort Options
          Row(
            children: [
              // Category Filter
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedCategory,
                  decoration: InputDecoration(
                    labelText: 'Category',
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                    ),
                  ),
                  items: _categories.map((category) {
                    return DropdownMenuItem<String>(
                      value: category,
                      child: Text(category),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedCategory = value;
                      });
                    }
                  },
                ),
              ),
              
              const SizedBox(width: 12),
              
              // Sort Options
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedSort,
                  decoration: InputDecoration(
                    labelText: 'Sort By',
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                    ),
                  ),
                  items: _sortOptions.map((option) {
                    return DropdownMenuItem<String>(
                      value: option,
                      child: Text(option),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedSort = value;
                      });
                    }
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    // This would be replaced with actual filtered data from Firestore
    final filteredBooks = List.generate(
      3,
      (index) => {
        'title': 'Search Result ${index + 1}',
        'totalSections': 100 + index * 50,
        'progress': 0.0,
        'isPinned': false,
        'isNew': false,
      },
    );
    
    if (filteredBooks.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: AppConstants.textSecondaryColor,
            ),
            SizedBox(height: 16),
            Text(
              'No books found',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppConstants.textPrimaryColor,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Try a different search term',
              style: TextStyle(
                fontSize: 14,
                color: AppConstants.textSecondaryColor,
              ),
            ),
          ],
        ),
      );
    }
    
    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: filteredBooks.length,
      itemBuilder: (context, index) {
        final book = filteredBooks[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: BookCard(
            title: book['title'] as String,
            totalSections: book['totalSections'] as int,
            progress: book['progress'] as double,
            isPinned: book['isPinned'] as bool,
            isNew: book['isNew'] as bool,
            onTap: () {
              // Navigate to book details
            },
          ),
        );
      },
    );
  }

  Widget _buildCategorizedBooks() {
    return ListView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      children: [
        // Constitutional Law Books
        SectionDivider(
          title: 'Constitutional Law',
          actionText: 'View All',
          onActionTap: () {
            // Navigate to constitutional law books
          },
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: 3, // This will be dynamic
          itemBuilder: (context, index) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: BookCard(
                title: 'Constitution of India ${index + 1}',
                totalSections: 395 + index * 5,
                progress: 0.1 + (index * 0.15),
                isPinned: index == 0,
                isNew: false,
                onTap: () {
                  // Navigate to book details
                },
              ),
            );
          },
        ),
        
        const SizedBox(height: 24),
        
        // Criminal Law Books
        SectionDivider(
          title: 'Criminal Law',
          actionText: 'View All',
          onActionTap: () {
            // Navigate to criminal law books
          },
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: 3, // This will be dynamic
          itemBuilder: (context, index) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: BookCard(
                title: 'Indian Penal Code ${index + 1}',
                totalSections: 511 + index * 10,
                progress: 0.3 + (index * 0.2),
                isPinned: index == 1,
                isNew: index == 0,
                onTap: () {
                  // Navigate to book details
                },
              ),
            );
          },
        ),
        
        const SizedBox(height: 24),
        
        // Civil Law Books
        SectionDivider(
          title: 'Civil Law',
          actionText: 'View All',
          onActionTap: () {
            // Navigate to civil law books
          },
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: 3, // This will be dynamic
          itemBuilder: (context, index) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: BookCard(
                title: 'Civil Procedure Code ${index + 1}',
                totalSections: 158 + index * 15,
                progress: 0.2 + (index * 0.1),
                isPinned: index == 2,
                isNew: false,
                onTap: () {
                  // Navigate to book details
                },
              ),
            );
          },
        ),
        
        const SizedBox(height: 100), // Extra space for bottom navigation
      ],
    );
  }
}

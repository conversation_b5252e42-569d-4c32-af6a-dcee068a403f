import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../services/sample_data_service.dart';
import '../models/book.dart';
import '../widgets/book_card.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<Book> _searchResults = [];
  List<Book> _allBooks = [];
  bool _isLoading = false;
  String _currentQuery = '';

  @override
  void initState() {
    super.initState();
    _allBooks = SampleDataService.getSampleBooks();
    _searchResults = _allBooks;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _performSearch(String query) {
    setState(() {
      _isLoading = true;
      _currentQuery = query;
    });

    // Simulate search delay
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        setState(() {
          if (query.isEmpty) {
            _searchResults = _allBooks;
          } else {
            _searchResults = SampleDataService.searchBooks(query);
          }
          _isLoading = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Search Books'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Search Bar
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextField(
              controller: _searchController,
              onChanged: _performSearch,
              decoration: InputDecoration(
                hintText: 'Search books, laws, sections...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _performSearch('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[100],
              ),
            ),
          ),

          // Search Results
          Expanded(
            child: _buildSearchResults(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_currentQuery.isNotEmpty && _searchResults.isEmpty) {
      return _buildEmptyResults();
    }

    if (_currentQuery.isEmpty) {
      return _buildSearchSuggestions();
    }

    return _buildResultsList();
  }

  Widget _buildEmptyResults() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No results found for "$_currentQuery"',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppConstants.textSecondaryColor,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Try searching with different keywords',
            style: TextStyle(
              fontSize: 14,
              color: AppConstants.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchSuggestions() {
    final tags = SampleDataService.getSampleTags();
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Popular Tags',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: tags.map((tag) {
              return ActionChip(
                label: Text(tag.name),
                onPressed: () {
                  _searchController.text = tag.name;
                  _performSearch(tag.name);
                },
                backgroundColor: AppConstants.primaryColor.withOpacity(0.1),
                labelStyle: const TextStyle(
                  color: AppConstants.primaryColor,
                ),
              );
            }).toList(),
          ),
          const SizedBox(height: 32),
          const Text(
            'Recent Searches',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 16),
          // TODO: Implement recent searches from local storage
          const Text(
            'No recent searches',
            style: TextStyle(
              color: AppConstants.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final book = _searchResults[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: BookCard(
            title: book.title,
            totalSections: book.totalSections,
            progress: book.progress,
            isPinned: book.isPinned,
            isNew: book.isNew,
            onTap: () {
              _navigateToBookDetails(book);
            },
            onPinToggle: () {
              _toggleBookPin(book);
            },
          ),
        );
      },
    );
  }

  void _navigateToBookDetails(Book book) {
    // TODO: Navigate to book details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening ${book.title}...'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _toggleBookPin(Book book) {
    setState(() {
      book.isPinned = !book.isPinned;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          book.isPinned ? 'Book pinned!' : 'Book unpinned!',
        ),
        duration: const Duration(seconds: 1),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../constants/app_constants.dart';
import '../models/book.dart';
import '../models/section.dart';
import '../widgets/custom_app_bar.dart';

class SectionReaderScreen extends StatefulWidget {
  final Book book;
  final Section section;
  final List<Section> sections;

  const SectionReaderScreen({
    super.key,
    required this.book,
    required this.section,
    required this.sections,
  });

  @override
  State<SectionReaderScreen> createState() => _SectionReaderScreenState();
}

class _SectionReaderScreenState extends State<SectionReaderScreen> {
  late ScrollController _scrollController;
  double _fontSize = 16.0;
  bool _isDarkMode = false;
  bool _isBookmarked = false;
  int _currentSectionIndex = 0;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _isBookmarked = widget.section.isBookmarked;
    _currentSectionIndex = widget.sections.indexOf(widget.section);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Section get _currentSection => widget.sections[_currentSectionIndex];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: _isDarkMode ? Colors.grey[900] : AppConstants.backgroundColor,
      appBar: CustomAppBar(
        title: widget.book.title,
        showBackButton: true,
        showSearch: false,
        backgroundColor: _isDarkMode ? Colors.grey[850] : null,
        actions: [
          IconButton(
            onPressed: _toggleBookmark,
            icon: Icon(
              _isBookmarked ? Icons.bookmark : Icons.bookmark_border,
              color: _isBookmarked ? AppConstants.primaryColor : Colors.white,
            ),
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings, size: 20),
                    SizedBox(width: 8),
                    Text('Reading Settings'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'share',
                child: Row(
                  children: [
                    Icon(Icons.share, size: 20),
                    SizedBox(width: 8),
                    Text('Share Section'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'copy',
                child: Row(
                  children: [
                    Icon(Icons.copy, size: 20),
                    SizedBox(width: 8),
                    Text('Copy Text'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Section Header
          _buildSectionHeader(),
          
          // Reading Content
          Expanded(
            child: SingleChildScrollView(
              controller: _scrollController,
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Section Title
                  Text(
                    _currentSection.title,
                    style: TextStyle(
                      fontSize: _fontSize + 4,
                      fontWeight: FontWeight.bold,
                      color: _isDarkMode ? Colors.white : AppConstants.textPrimaryColor,
                      height: 1.3,
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Section Content
                  Text(
                    _currentSection.content,
                    style: TextStyle(
                      fontSize: _fontSize,
                      color: _isDarkMode ? Colors.grey[300] : AppConstants.textPrimaryColor,
                      height: 1.6,
                    ),
                  ),
                  
                  const SizedBox(height: 100), // Extra space for bottom navigation
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomNavigation(),
      floatingActionButton: _buildReadingSettings(),
    );
  }

  Widget _buildSectionHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: _isDarkMode ? Colors.grey[850] : Colors.white,
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Progress Bar
          Row(
            children: [
              Text(
                'Section ${_currentSection.sectionNumber} of ${widget.sections.length}',
                style: TextStyle(
                  fontSize: 12,
                  color: _isDarkMode ? Colors.grey[400] : AppConstants.textSecondaryColor,
                ),
              ),
              const Spacer(),
              Text(
                '${((_currentSectionIndex + 1) / widget.sections.length * 100).toInt()}%',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: _isDarkMode ? Colors.grey[300] : AppConstants.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: (_currentSectionIndex + 1) / widget.sections.length,
            backgroundColor: _isDarkMode ? Colors.grey[700] : Colors.grey[300],
            valueColor: const AlwaysStoppedAnimation<Color>(AppConstants.primaryColor),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigation() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: _isDarkMode ? Colors.grey[850] : Colors.white,
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 4,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Previous Button
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _currentSectionIndex > 0 ? _goToPreviousSection : null,
              icon: const Icon(Icons.arrow_back),
              label: const Text('Previous'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _isDarkMode ? Colors.grey[700] : Colors.grey[200],
                foregroundColor: _isDarkMode ? Colors.white : AppConstants.textPrimaryColor,
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Next Button
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _currentSectionIndex < widget.sections.length - 1 ? _goToNextSection : null,
              icon: const Icon(Icons.arrow_forward),
              label: const Text('Next'),
              iconAlignment: IconAlignment.end,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConstants.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReadingSettings() {
    return FloatingActionButton(
      onPressed: _showReadingSettings,
      backgroundColor: AppConstants.primaryColor,
      child: const Icon(Icons.text_fields, color: Colors.white),
    );
  }

  void _toggleBookmark() {
    setState(() {
      _isBookmarked = !_isBookmarked;
      _currentSection.isBookmarked = _isBookmarked;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          _isBookmarked ? 'Section bookmarked!' : 'Bookmark removed!',
        ),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'settings':
        _showReadingSettings();
        break;
      case 'share':
        _shareSection();
        break;
      case 'copy':
        _copyText();
        break;
    }
  }

  void _goToPreviousSection() {
    if (_currentSectionIndex > 0) {
      setState(() {
        _currentSectionIndex--;
        _isBookmarked = _currentSection.isBookmarked;
      });
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _goToNextSection() {
    if (_currentSectionIndex < widget.sections.length - 1) {
      setState(() {
        _currentSectionIndex++;
        _isBookmarked = _currentSection.isBookmarked;
      });
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _showReadingSettings() {
    showModalBottomSheet(
      context: context,
      backgroundColor: _isDarkMode ? Colors.grey[850] : Colors.white,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Reading Settings',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _isDarkMode ? Colors.white : AppConstants.textPrimaryColor,
              ),
            ),
            const SizedBox(height: 24),

            // Font Size
            Row(
              children: [
                Icon(
                  Icons.text_fields,
                  color: _isDarkMode ? Colors.grey[400] : AppConstants.textSecondaryColor,
                ),
                const SizedBox(width: 12),
                Text(
                  'Font Size',
                  style: TextStyle(
                    fontSize: 16,
                    color: _isDarkMode ? Colors.white : AppConstants.textPrimaryColor,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: _fontSize > 12 ? () => _changeFontSize(-2) : null,
                  icon: const Icon(Icons.remove),
                ),
                Text(
                  '${_fontSize.toInt()}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: _isDarkMode ? Colors.white : AppConstants.textPrimaryColor,
                  ),
                ),
                IconButton(
                  onPressed: _fontSize < 24 ? () => _changeFontSize(2) : null,
                  icon: const Icon(Icons.add),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Dark Mode Toggle
            Row(
              children: [
                Icon(
                  Icons.dark_mode,
                  color: _isDarkMode ? Colors.grey[400] : AppConstants.textSecondaryColor,
                ),
                const SizedBox(width: 12),
                Text(
                  'Dark Mode',
                  style: TextStyle(
                    fontSize: 16,
                    color: _isDarkMode ? Colors.white : AppConstants.textPrimaryColor,
                  ),
                ),
                const Spacer(),
                Switch(
                  value: _isDarkMode,
                  onChanged: (value) {
                    setState(() {
                      _isDarkMode = value;
                    });
                    Navigator.pop(context);
                  },
                  activeColor: AppConstants.primaryColor,
                ),
              ],
            ),

            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  void _changeFontSize(double delta) {
    setState(() {
      _fontSize = (_fontSize + delta).clamp(12.0, 24.0);
    });
  }

  void _shareSection() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality would open here'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _copyText() {
    final text = '${_currentSection.title}\n\n${_currentSection.content}';
    Clipboard.setData(ClipboardData(text: text));

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Section text copied to clipboard'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import '../constants/app_constants.dart';

class SectionCard extends StatelessWidget {
  final String sectionTitle;
  final String bookTitle;
  final String content;
  final bool isFavorite;
  final List<String> tags;
  final VoidCallback onTap;
  final VoidCallback? onFavoriteToggle;
  final Function(String)? onTagTap;

  const SectionCard({
    super.key,
    required this.sectionTitle,
    required this.bookTitle,
    required this.content,
    required this.isFavorite,
    required this.tags,
    required this.onTap,
    this.onFavoriteToggle,
    this.onTagTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row
              Row(
                children: [
                  // Section Icon
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: AppConstants.secondaryColor,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: const Icon(
                      Icons.article_outlined,
                      color: AppConstants.primaryColor,
                      size: 16,
                    ),
                  ),
                  
                  const SizedBox(width: 12),
                  
                  // Section Title
                  Expanded(
                    child: Text(
                      sectionTitle,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppConstants.textPrimaryColor,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  
                  // Favorite Button
                  if (onFavoriteToggle != null)
                    IconButton(
                      onPressed: onFavoriteToggle,
                      icon: Icon(
                        isFavorite ? Icons.favorite : Icons.favorite_border,
                        color: isFavorite 
                            ? AppConstants.errorColor 
                            : AppConstants.textSecondaryColor,
                        size: 20,
                      ),
                      padding: const EdgeInsets.all(4),
                      constraints: const BoxConstraints(),
                    ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // Book Title
              Text(
                bookTitle,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppConstants.primaryColor,
                ),
              ),
              
              const SizedBox(height: 12),
              
              // Content Preview
              Text(
                content,
                style: const TextStyle(
                  fontSize: 14,
                  color: AppConstants.textSecondaryColor,
                  height: 1.4,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              
              const SizedBox(height: 16),
              
              // Tags
              if (tags.isNotEmpty) ...[
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: tags.map((tag) {
                    return GestureDetector(
                      onTap: () => onTagTap?.call(tag),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: AppConstants.secondaryColor,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: AppConstants.primaryColor.withOpacity(0.3),
                          ),
                        ),
                        child: Text(
                          tag,
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppConstants.primaryColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
                
                const SizedBox(height: 12),
              ],
              
              // Action Buttons
              Row(
                children: [
                  // Read More Button
                  Expanded(
                    child: TextButton.icon(
                      onPressed: onTap,
                      icon: const Icon(
                        Icons.read_more,
                        size: 16,
                      ),
                      label: const Text(
                        'Read More',
                        style: TextStyle(fontSize: 12),
                      ),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                      ),
                    ),
                  ),
                  
                  // Share Button
                  TextButton.icon(
                    onPressed: () {
                      _shareSection(context);
                    },
                    icon: const Icon(
                      Icons.share_outlined,
                      size: 16,
                      color: AppConstants.textSecondaryColor,
                    ),
                    label: const Text(
                      'Share',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppConstants.textSecondaryColor,
                      ),
                    ),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                    ),
                  ),
                  
                  // More Options
                  IconButton(
                    onPressed: () {
                      _showMoreOptions(context);
                    },
                    icon: const Icon(
                      Icons.more_vert,
                      size: 20,
                      color: AppConstants.textSecondaryColor,
                    ),
                    padding: const EdgeInsets.all(4),
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _shareSection(BuildContext context) {
    // Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality will be implemented'),
        backgroundColor: AppConstants.primaryColor,
      ),
    );
  }

  void _showMoreOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.copy_outlined),
              title: const Text('Copy Text'),
              onTap: () {
                Navigator.of(context).pop();
                // Implement copy functionality
              },
            ),
            ListTile(
              leading: const Icon(Icons.bookmark_outline),
              title: const Text('Add to Reading List'),
              onTap: () {
                Navigator.of(context).pop();
                // Add to reading list
              },
            ),
            ListTile(
              leading: const Icon(Icons.local_offer_outlined),
              title: const Text('Add Tag'),
              onTap: () {
                Navigator.of(context).pop();
                // Show add tag dialog
              },
            ),
            ListTile(
              leading: const Icon(Icons.report_outlined),
              title: const Text('Report Issue'),
              onTap: () {
                Navigator.of(context).pop();
                // Report issue
              },
            ),
          ],
        ),
      ),
    );
  }
}

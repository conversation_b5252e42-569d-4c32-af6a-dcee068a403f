import 'package:flutter/material.dart';
import '../constants/app_constants.dart';

class BookCard extends StatelessWidget {
  final String title;
  final int totalSections;
  final double progress;
  final bool isPinned;
  final bool isNew;
  final VoidCallback onTap;
  final VoidCallback? onPinToggle;

  const BookCard({
    super.key,
    required this.title,
    required this.totalSections,
    required this.progress,
    required this.isPinned,
    required this.isNew,
    required this.onTap,
    this.onPinToggle,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: LayoutBuilder(
            builder: (context, constraints) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
              // Header Row
              Row(
                children: [
                  // Book Icon
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppConstants.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.book,
                      color: AppConstants.primaryColor,
                      size: 24,
                    ),
                  ),
                  
                  const SizedBox(width: 12),
                  
                  // Title and Badges
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                title,
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: AppConstants.textPrimaryColor,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            
                            // Badges
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                if (isNew)
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 6,
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color: AppConstants.successColor,
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    child: const Text(
                                      'NEW',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 10,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                
                                if (isNew && isPinned) const SizedBox(width: 4),
                                
                                if (isPinned)
                                  const Icon(
                                    Icons.push_pin,
                                    color: AppConstants.primaryColor,
                                    size: 16,
                                  ),
                              ],
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 4),
                        
                        // Sections Count
                        Row(
                          children: [
                            const Icon(
                              Icons.article_outlined,
                              size: 14,
                              color: AppConstants.textSecondaryColor,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '$totalSections sections',
                              style: const TextStyle(
                                fontSize: 12,
                                color: AppConstants.textSecondaryColor,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),

              // Progress Section
              if (progress > 0) ...[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Reading Progress',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppConstants.textSecondaryColor,
                      ),
                    ),
                    Text(
                      '${(progress * 100).toInt()}%',
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 6),

                // Progress Bar
                LinearProgressIndicator(
                  value: progress,
                  backgroundColor: Colors.grey[200],
                  valueColor: const AlwaysStoppedAnimation<Color>(
                    AppConstants.primaryColor,
                  ),
                  minHeight: 3,
                ),
                const SizedBox(height: 8),
              ] else ...[
                // Start Reading Button
                SizedBox(
                  width: double.infinity,
                  height: 32,
                  child: OutlinedButton(
                    onPressed: onTap,
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      side: const BorderSide(color: AppConstants.primaryColor),
                    ),
                    child: const Text(
                      'Start Reading',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
              ],
              
              // Action Buttons
              if (constraints.maxHeight > 120) // Only show if there's enough space
                Row(
                  children: [
                    // Pin/Unpin Button
                    if (onPinToggle != null)
                      Expanded(
                        child: TextButton.icon(
                          onPressed: onPinToggle,
                          icon: Icon(
                            isPinned ? Icons.push_pin : Icons.push_pin_outlined,
                            size: 14,
                            color: isPinned
                                ? AppConstants.primaryColor
                                : AppConstants.textSecondaryColor,
                          ),
                          label: Text(
                            isPinned ? 'Pinned' : 'Pin',
                            style: TextStyle(
                              fontSize: 10,
                              color: isPinned
                                  ? AppConstants.primaryColor
                                  : AppConstants.textSecondaryColor,
                            ),
                          ),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 4,
                              vertical: 2,
                            ),
                            minimumSize: const Size(0, 24),
                          ),
                        ),
                      ),

                    // Share Button
                    Expanded(
                      child: TextButton.icon(
                        onPressed: () {
                          // Implement share functionality
                        },
                        icon: const Icon(
                          Icons.share_outlined,
                          size: 14,
                          color: AppConstants.textSecondaryColor,
                        ),
                        label: const Text(
                          'Share',
                          style: TextStyle(
                            fontSize: 10,
                            color: AppConstants.textSecondaryColor,
                          ),
                        ),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 4,
                            vertical: 2,
                          ),
                          minimumSize: const Size(0, 24),
                        ),
                      ),
                    ),

                    // More Options Button
                    IconButton(
                      onPressed: () {
                        _showMoreOptions(context);
                      },
                      icon: const Icon(
                        Icons.more_vert,
                        size: 16,
                        color: AppConstants.textSecondaryColor,
                      ),
                      padding: const EdgeInsets.all(2),
                      constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
                    ),
                  ],
                ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  void _showMoreOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.info_outline),
              title: const Text('Book Details'),
              onTap: () {
                Navigator.of(context).pop();
                // Navigate to book details
              },
            ),
            ListTile(
              leading: const Icon(Icons.download_outlined),
              title: const Text('Download for Offline'),
              onTap: () {
                Navigator.of(context).pop();
                // Implement download functionality
              },
            ),
            ListTile(
              leading: const Icon(Icons.bookmark_outline),
              title: const Text('Add to Reading List'),
              onTap: () {
                Navigator.of(context).pop();
                // Add to reading list
              },
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../providers/auth_provider.dart';
import '../screens/search_screen.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final bool showSearch;
  final bool showNotifications;
  final VoidCallback? onSearchTap;
  final VoidCallback? onNotificationTap;

  const CustomAppBar({
    super.key,
    required this.title,
    this.showSearch = false,
    this.showNotifications = false,
    this.onSearchTap,
    this.onNotificationTap,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: AppConstants.primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      automaticallyImplyLeading: false,
      title: Row(
        children: [
          // Profile Photo
          Consumer<AuthProvider>(
            builder: (context, authProvider, child) {
              return GestureDetector(
                onTap: () {
                  // Navigate to profile or show profile menu
                },
                child: CircleAvatar(
                  radius: 20,
                  backgroundColor: Colors.white.withOpacity(0.2),
                  backgroundImage: authProvider.user?.photoURL != null
                      ? NetworkImage(authProvider.user!.photoURL!)
                      : null,
                  child: authProvider.user?.photoURL == null
                      ? const Icon(
                          Icons.person,
                          color: Colors.white,
                          size: 24,
                        )
                      : null,
                ),
              );
            },
          ),
          
          const SizedBox(width: 16),
          
          // App Name/Title
          Expanded(
            child: Text(
              title == 'Home' ? AppConstants.appName : title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Action Buttons
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Search Button
              if (showSearch)
                IconButton(
                  onPressed: onSearchTap ?? () => _navigateToSearch(context),
                  icon: const Icon(
                    Icons.search,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              
              // Notifications Button
              if (showNotifications)
                Stack(
                  children: [
                    IconButton(
                      onPressed: onNotificationTap ?? () => _showNotificationsDialog(context),
                      icon: const Icon(
                        Icons.notifications_outlined,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    // Notification Badge
                    Positioned(
                      right: 8,
                      top: 8,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: AppConstants.errorColor,
                          borderRadius: BorderRadius.circular(6),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 12,
                          minHeight: 12,
                        ),
                        child: const Text(
                          '3', // This will be dynamic based on actual notifications
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 8,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ],
      ),
    );
  }

  void _navigateToSearch(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const SearchScreen(),
      ),
    );
  }

  void _showNotificationsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Notifications'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.book, color: AppConstants.primaryColor),
              title: const Text('New Book Added'),
              subtitle: const Text('Indian Penal Code 2024 Edition'),
              trailing: const Text('2h ago'),
              onTap: () {
                Navigator.of(context).pop();
                // Navigate to the new book
              },
            ),
            ListTile(
              leading: const Icon(Icons.update, color: AppConstants.warningColor),
              title: const Text('Book Amendment'),
              subtitle: const Text('Constitution of India - Article 370'),
              trailing: const Text('1d ago'),
              onTap: () {
                Navigator.of(context).pop();
                // Navigate to the amended section
              },
            ),
            ListTile(
              leading: const Icon(Icons.star, color: AppConstants.successColor),
              title: const Text('Premium Feature'),
              subtitle: const Text('Your free trial expires in 30 days'),
              trailing: const Text('3d ago'),
              onTap: () {
                Navigator.of(context).pop();
                // Navigate to subscription page
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Navigate to notifications page
            },
            child: const Text('View All'),
          ),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

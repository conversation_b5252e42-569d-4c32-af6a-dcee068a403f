import '../models/book.dart';
import '../models/section.dart';
import '../models/tag.dart';

class SampleDataService {
  static List<Book> getSampleBooks() {
    return [
      Book(
        id: '1',
        title: 'Indian Constitution',
        description: 'Complete guide to the Indian Constitution with all amendments and articles.',
        totalSections: 395,
        tags: ['Constitution', 'Fundamental Rights', 'Government'],
        isPinned: true,
        isNew: false,
        progress: 0.65,
        lastReadAt: DateTime.now().subtract(const Duration(days: 2)),
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
      Book(
        id: '2',
        title: 'Indian Penal Code (IPC)',
        description: 'Comprehensive coverage of criminal law in India.',
        totalSections: 511,
        tags: ['Criminal Law', 'IPC', 'Offenses'],
        isPinned: false,
        isNew: true,
        progress: 0.0,
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      Book(
        id: '3',
        title: 'Code of Civil Procedure (CPC)',
        description: 'Procedural law for civil cases in Indian courts.',
        totalSections: 158,
        tags: ['Civil Law', 'Procedure', 'Courts'],
        isPinned: true,
        isNew: false,
        progress: 0.35,
        lastReadAt: DateTime.now().subtract(const Duration(days: 5)),
        createdAt: DateTime.now().subtract(const Duration(days: 60)),
        updatedAt: DateTime.now().subtract(const Duration(days: 5)),
      ),
      Book(
        id: '4',
        title: 'Contract Act 1872',
        description: 'Laws governing contracts and agreements in India.',
        totalSections: 266,
        tags: ['Contract Law', 'Business Law', 'Agreements'],
        isPinned: false,
        isNew: false,
        progress: 0.80,
        lastReadAt: DateTime.now().subtract(const Duration(days: 1)),
        createdAt: DateTime.now().subtract(const Duration(days: 90)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      Book(
        id: '5',
        title: 'Evidence Act 1872',
        description: 'Rules of evidence in Indian legal proceedings.',
        totalSections: 167,
        tags: ['Evidence', 'Court Procedure', 'Legal Process'],
        isPinned: false,
        isNew: true,
        progress: 0.15,
        lastReadAt: DateTime.now().subtract(const Duration(days: 3)),
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
        updatedAt: DateTime.now().subtract(const Duration(days: 3)),
      ),
      Book(
        id: '6',
        title: 'Companies Act 2013',
        description: 'Corporate law and company regulations in India.',
        totalSections: 470,
        tags: ['Corporate Law', 'Business', 'Companies'],
        isPinned: false,
        isNew: false,
        progress: 0.45,
        lastReadAt: DateTime.now().subtract(const Duration(days: 7)),
        createdAt: DateTime.now().subtract(const Duration(days: 120)),
        updatedAt: DateTime.now().subtract(const Duration(days: 7)),
      ),
    ];
  }

  static List<Tag> getSampleTags() {
    return [
      Tag(
        id: '1',
        name: 'Constitution',
        color: '#FF6B6B',
        bookCount: 1,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
      Tag(
        id: '2',
        name: 'Criminal Law',
        color: '#4ECDC4',
        bookCount: 1,
        createdAt: DateTime.now().subtract(const Duration(days: 25)),
      ),
      Tag(
        id: '3',
        name: 'Civil Law',
        color: '#45B7D1',
        bookCount: 1,
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
      ),
      Tag(
        id: '4',
        name: 'Contract Law',
        color: '#96CEB4',
        bookCount: 1,
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
      ),
      Tag(
        id: '5',
        name: 'Evidence',
        color: '#FFEAA7',
        bookCount: 1,
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
      ),
      Tag(
        id: '6',
        name: 'Corporate Law',
        color: '#DDA0DD',
        bookCount: 1,
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
      ),
    ];
  }

  static List<Section> getSampleSections(String bookId) {
    switch (bookId) {
      case '1': // Indian Constitution
        return [
          Section(
            id: '1-1',
            bookId: bookId,
            title: 'Article 1: Name and Territory of the Union',
            content: 'India, that is Bharat, shall be a Union of States.',
            sectionNumber: 1,
            isBookmarked: true,
            createdAt: DateTime.now().subtract(const Duration(days: 30)),
          ),
          Section(
            id: '1-2',
            bookId: bookId,
            title: 'Article 14: Equality before Law',
            content: 'The State shall not deny to any person equality before the law or the equal protection of the laws within the territory of India.',
            sectionNumber: 14,
            isBookmarked: false,
            createdAt: DateTime.now().subtract(const Duration(days: 30)),
          ),
          Section(
            id: '1-3',
            bookId: bookId,
            title: 'Article 19: Protection of certain rights regarding freedom of speech etc.',
            content: 'All citizens shall have the right to freedom of speech and expression.',
            sectionNumber: 19,
            isBookmarked: true,
            createdAt: DateTime.now().subtract(const Duration(days: 30)),
          ),
        ];
      case '2': // IPC
        return [
          Section(
            id: '2-1',
            bookId: bookId,
            title: 'Section 1: Title and extent of operation of the Code',
            content: 'This Act shall be called the Indian Penal Code, and shall extend to the whole of India.',
            sectionNumber: 1,
            isBookmarked: false,
            createdAt: DateTime.now().subtract(const Duration(days: 5)),
          ),
          Section(
            id: '2-2',
            bookId: bookId,
            title: 'Section 302: Punishment for murder',
            content: 'Whoever commits murder shall be punished with death, or imprisonment for life, and shall also be liable to fine.',
            sectionNumber: 302,
            isBookmarked: true,
            createdAt: DateTime.now().subtract(const Duration(days: 5)),
          ),
        ];
      default:
        return [];
    }
  }

  static List<Book> searchBooks(String query) {
    final books = getSampleBooks();
    if (query.isEmpty) return books;
    
    return books.where((book) {
      return book.title.toLowerCase().contains(query.toLowerCase()) ||
             book.description.toLowerCase().contains(query.toLowerCase()) ||
             book.tags.any((tag) => tag.toLowerCase().contains(query.toLowerCase()));
    }).toList();
  }

  static List<Book> getBooksByTag(String tagName) {
    final books = getSampleBooks();
    return books.where((book) => book.tags.contains(tagName)).toList();
  }

  static List<Book> getFavoriteBooks() {
    final books = getSampleBooks();
    return books.where((book) => book.isPinned).toList();
  }

  static List<Book> getRecentBooks() {
    final books = getSampleBooks();
    books.sort((a, b) {
      final aDate = a.lastReadAt ?? a.createdAt;
      final bDate = b.lastReadAt ?? b.createdAt;
      return bDate.compareTo(aDate);
    });
    return books.take(3).toList();
  }

  static List<Book> getNewBooks() {
    final books = getSampleBooks();
    return books.where((book) => book.isNew).toList();
  }

  static List<Book> getContinueReadingBooks() {
    final books = getSampleBooks();
    return books.where((book) => book.progress > 0 && book.progress < 1.0).toList();
  }
}

import '../models/book.dart';
import '../models/section.dart';
import '../models/tag.dart';

class SampleDataService {
  static List<Book> getSampleBooks() {
    return [
      Book(
        id: '1',
        title: 'Indian Constitution',
        description: 'Complete guide to the Indian Constitution with all amendments and articles.',
        totalSections: 395,
        tags: ['Constitution', 'Fundamental Rights', 'Government'],
        isPinned: true,
        isNew: false,
        progress: 0.65,
        lastReadAt: DateTime.now().subtract(const Duration(days: 2)),
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
      Book(
        id: '2',
        title: 'Indian Penal Code (IPC)',
        description: 'Comprehensive coverage of criminal law in India.',
        totalSections: 511,
        tags: ['Criminal Law', 'IPC', 'Offenses'],
        isPinned: false,
        isNew: true,
        progress: 0.0,
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      Book(
        id: '3',
        title: 'Code of Civil Procedure (CPC)',
        description: 'Procedural law for civil cases in Indian courts.',
        totalSections: 158,
        tags: ['Civil Law', 'Procedure', 'Courts'],
        isPinned: true,
        isNew: false,
        progress: 0.35,
        lastReadAt: DateTime.now().subtract(const Duration(days: 5)),
        createdAt: DateTime.now().subtract(const Duration(days: 60)),
        updatedAt: DateTime.now().subtract(const Duration(days: 5)),
      ),
      Book(
        id: '4',
        title: 'Contract Act 1872',
        description: 'Laws governing contracts and agreements in India.',
        totalSections: 266,
        tags: ['Contract Law', 'Business Law', 'Agreements'],
        isPinned: false,
        isNew: false,
        progress: 0.80,
        lastReadAt: DateTime.now().subtract(const Duration(days: 1)),
        createdAt: DateTime.now().subtract(const Duration(days: 90)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      Book(
        id: '5',
        title: 'Evidence Act 1872',
        description: 'Rules of evidence in Indian legal proceedings.',
        totalSections: 167,
        tags: ['Evidence', 'Court Procedure', 'Legal Process'],
        isPinned: false,
        isNew: true,
        progress: 0.15,
        lastReadAt: DateTime.now().subtract(const Duration(days: 3)),
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
        updatedAt: DateTime.now().subtract(const Duration(days: 3)),
      ),
      Book(
        id: '6',
        title: 'Companies Act 2013',
        description: 'Corporate law and company regulations in India.',
        totalSections: 470,
        tags: ['Corporate Law', 'Business', 'Companies'],
        isPinned: false,
        isNew: false,
        progress: 0.45,
        lastReadAt: DateTime.now().subtract(const Duration(days: 7)),
        createdAt: DateTime.now().subtract(const Duration(days: 120)),
        updatedAt: DateTime.now().subtract(const Duration(days: 7)),
      ),
    ];
  }

  static List<Tag> getSampleTags() {
    return [
      Tag(
        id: '1',
        name: 'Constitution',
        color: '#FF6B6B',
        bookCount: 1,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
      Tag(
        id: '2',
        name: 'Criminal Law',
        color: '#4ECDC4',
        bookCount: 1,
        createdAt: DateTime.now().subtract(const Duration(days: 25)),
      ),
      Tag(
        id: '3',
        name: 'Civil Law',
        color: '#45B7D1',
        bookCount: 1,
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
      ),
      Tag(
        id: '4',
        name: 'Contract Law',
        color: '#96CEB4',
        bookCount: 1,
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
      ),
      Tag(
        id: '5',
        name: 'Evidence',
        color: '#FFEAA7',
        bookCount: 1,
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
      ),
      Tag(
        id: '6',
        name: 'Corporate Law',
        color: '#DDA0DD',
        bookCount: 1,
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
      ),
    ];
  }



  static List<Book> searchBooks(String query) {
    final books = getSampleBooks();
    if (query.isEmpty) return books;
    
    return books.where((book) {
      return book.title.toLowerCase().contains(query.toLowerCase()) ||
             book.description.toLowerCase().contains(query.toLowerCase()) ||
             book.tags.any((tag) => tag.toLowerCase().contains(query.toLowerCase()));
    }).toList();
  }

  static List<Book> getBooksByTag(String tagName) {
    final books = getSampleBooks();
    return books.where((book) => book.tags.contains(tagName)).toList();
  }

  static List<Book> getFavoriteBooks() {
    final books = getSampleBooks();
    return books.where((book) => book.isPinned).toList();
  }

  static List<Book> getRecentBooks() {
    final books = getSampleBooks();
    books.sort((a, b) {
      final aDate = a.lastReadAt ?? a.createdAt;
      final bDate = b.lastReadAt ?? b.createdAt;
      return bDate.compareTo(aDate);
    });
    return books.take(3).toList();
  }

  static List<Book> getNewBooks() {
    final books = getSampleBooks();
    return books.where((book) => book.isNew).toList();
  }

  static List<Book> getContinueReadingBooks() {
    final books = getSampleBooks();
    return books.where((book) => book.progress > 0 && book.progress < 1.0).toList();
  }

  static List<Section> getSampleSections(String bookId) {
    final now = DateTime.now();

    // Generate sections based on book ID for consistency
    final sectionCount = bookId.hashCode % 20 + 10; // 10-30 sections

    return List.generate(sectionCount, (index) {
      final sectionNumber = index + 1;
      return Section(
        id: 'section_${bookId}_$sectionNumber',
        bookId: bookId,
        title: _getSectionTitle(bookId, sectionNumber),
        content: _getSectionContent(bookId, sectionNumber),
        sectionNumber: sectionNumber,
        isBookmarked: index % 7 == 0, // Every 7th section is bookmarked
        createdAt: now.subtract(Duration(days: index * 2)),
      );
    });
  }

  static String _getSectionTitle(String bookId, int sectionNumber) {
    if (bookId.contains('constitution')) {
      return 'Article $sectionNumber - Fundamental Rights and Duties';
    } else if (bookId.contains('penal')) {
      return 'Section $sectionNumber - Criminal Offenses and Penalties';
    } else if (bookId.contains('civil')) {
      return 'Order $sectionNumber - Civil Procedure Rules';
    } else if (bookId.contains('contract')) {
      return 'Section $sectionNumber - Contract Formation and Enforcement';
    } else if (bookId.contains('evidence')) {
      return 'Section $sectionNumber - Evidence and Proof';
    } else if (bookId.contains('companies')) {
      return 'Section $sectionNumber - Corporate Governance';
    } else {
      return 'Section $sectionNumber - Legal Provisions';
    }
  }

  static String _getSectionContent(String bookId, int sectionNumber) {
    if (bookId.contains('constitution')) {
      return 'This article establishes the fundamental rights guaranteed to all citizens under the Constitution of India. It outlines the scope, limitations, and enforcement mechanisms for these constitutional protections. The provisions ensure equality before law and equal protection of laws for all persons within the territory of India.';
    } else if (bookId.contains('penal')) {
      return 'This section defines criminal offenses and prescribes punishments under the Indian Penal Code. It covers the elements of the crime, mens rea requirements, and graduated penalties based on the severity of the offense. The section also addresses mitigating and aggravating circumstances.';
    } else if (bookId.contains('civil')) {
      return 'This order establishes the procedural framework for civil litigation in Indian courts. It specifies the requirements for pleadings, service of process, discovery procedures, and trial conduct. The rules ensure fair and efficient resolution of civil disputes.';
    } else if (bookId.contains('contract')) {
      return 'This section governs the formation, performance, and enforcement of contracts under Indian law. It defines the essential elements of a valid contract, including offer, acceptance, consideration, and legal capacity. The section also addresses breach of contract and available remedies.';
    } else if (bookId.contains('evidence')) {
      return 'This section establishes the rules for admissibility and evaluation of evidence in legal proceedings. It covers different types of evidence, burden of proof, presumptions, and the examination of witnesses. The provisions ensure fair and reliable fact-finding in judicial proceedings.';
    } else if (bookId.contains('companies')) {
      return 'This section regulates corporate governance and management under the Companies Act. It defines the duties and responsibilities of directors, requirements for board meetings, and compliance obligations. The provisions ensure transparency and accountability in corporate operations.';
    } else {
      return 'This section contains important legal provisions that govern various aspects of law and procedure. It establishes rights, duties, and obligations of parties involved in legal matters. The section provides guidance for proper interpretation and application of legal principles.';
    }
  }
}

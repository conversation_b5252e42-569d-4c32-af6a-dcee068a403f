import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import '../constants/app_constants.dart';

class FirestoreService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  // User Profile Methods
  static Future<void> createUserProfile(User user, String name) async {
    try {
      final userProfileRef = _firestore
          .collection(AppConstants.userProfilesCollection)
          .doc(user.uid);
      
      final userSettingsRef = _firestore
          .collection(AppConstants.userSettingsCollection)
          .doc(user.uid);
      
      final userNotificationPrefsRef = _firestore
          .collection(AppConstants.userNotificationPreferencesCollection)
          .doc(user.uid);
      
      final now = DateTime.now();
      final freeTrialEnd = now.add(Duration(days: AppConstants.freeTrialDays));
      
      // Create user profile
      await userProfileRef.set({
        'uid': user.uid,
        'name': name,
        'email': user.email,
        'photoURL': user.photoURL,
        'phoneNumber': user.phoneNumber,
        'created_at': now,
        'updated_at': now,
      });
      
      // Create user settings
      await userSettingsRef.set({
        'uid': user.uid,
        'theme_mode': 'light',
        'font_size': 'medium',
        'text_alignment': 'left',
        'line_spacing': 'medium',
        'created_at': now,
        'updated_at': now,
      });
      
      // Create notification preferences
      await userNotificationPrefsRef.set({
        'uid': user.uid,
        'new_books': true,
        'amendments': true,
        'subscription_reminders': true,
        'created_at': now,
        'updated_at': now,
      });
      
      // Create subscription record with free trial
      await _firestore
          .collection(AppConstants.subscriptionsCollection)
          .doc(user.uid)
          .set({
        'uid': user.uid,
        'status': 'free_trial',
        'trial_start': now,
        'trial_end': freeTrialEnd,
        'subscription_plan': null,
        'subscription_start': null,
        'subscription_end': null,
        'payment_method': null,
        'created_at': now,
        'updated_at': now,
      });
      
    } catch (e) {
      debugPrint('Error creating user profile: $e');
      rethrow;
    }
  }
  
  static Future<void> createOrUpdateUserProfile(User user) async {
    try {
      final userProfileRef = _firestore
          .collection(AppConstants.userProfilesCollection)
          .doc(user.uid);
      
      final userDoc = await userProfileRef.get();
      
      if (userDoc.exists) {
        // Update existing user
        await userProfileRef.update({
          'name': user.displayName,
          'email': user.email,
          'photoURL': user.photoURL,
          'phoneNumber': user.phoneNumber,
          'updated_at': DateTime.now(),
        });
      } else {
        // Create new user
        await createUserProfile(user, user.displayName ?? 'User');
      }
    } catch (e) {
      debugPrint('Error creating/updating user profile: $e');
      rethrow;
    }
  }
  
  static Future<void> updateUserLastLogin(String uid) async {
    try {
      await _firestore
          .collection(AppConstants.userProfilesCollection)
          .doc(uid)
          .update({
        'last_login': DateTime.now(),
      });
    } catch (e) {
      debugPrint('Error updating user last login: $e');
    }
  }
  
  // Subscription Methods
  static Future<Map<String, dynamic>?> getUserSubscriptionStatus(String uid) async {
    try {
      final subscriptionDoc = await _firestore
          .collection(AppConstants.subscriptionsCollection)
          .doc(uid)
          .get();
      
      if (subscriptionDoc.exists) {
        return subscriptionDoc.data();
      }
      return null;
    } catch (e) {
      debugPrint('Error getting user subscription status: $e');
      return null;
    }
  }
  
  static Future<bool> isUserPremium(String uid) async {
    try {
      final subscriptionData = await getUserSubscriptionStatus(uid);
      
      if (subscriptionData == null) {
        return false;
      }
      
      final status = subscriptionData['status'];
      
      if (status == 'active') {
        return true;
      } else if (status == 'free_trial') {
        final trialEnd = (subscriptionData['trial_end'] as Timestamp).toDate();
        return DateTime.now().isBefore(trialEnd);
      }
      
      return false;
    } catch (e) {
      debugPrint('Error checking if user is premium: $e');
      return false;
    }
  }
  
  // Book Methods
  static Future<List<Map<String, dynamic>>> getBooks({int limit = 10}) async {
    try {
      final querySnapshot = await _firestore
          .collection(AppConstants.booksCollection)
          .orderBy('created_at', descending: true)
          .limit(limit)
          .get();
      
      return querySnapshot.docs
          .map((doc) => {
                'id': doc.id,
                ...doc.data(),
              })
          .toList();
    } catch (e) {
      debugPrint('Error getting books: $e');
      return [];
    }
  }
  
  static Future<List<Map<String, dynamic>>> getNewBooks({int limit = 5}) async {
    try {
      final querySnapshot = await _firestore
          .collection(AppConstants.booksCollection)
          .where('is_new', isEqualTo: true)
          .orderBy('created_at', descending: true)
          .limit(limit)
          .get();
      
      return querySnapshot.docs
          .map((doc) => {
                'id': doc.id,
                ...doc.data(),
              })
          .toList();
    } catch (e) {
      debugPrint('Error getting new books: $e');
      return [];
    }
  }
  
  static Future<Map<String, dynamic>?> getBookById(String bookId) async {
    try {
      final docSnapshot = await _firestore
          .collection(AppConstants.booksCollection)
          .doc(bookId)
          .get();
      
      if (docSnapshot.exists) {
        return {
          'id': docSnapshot.id,
          ...docSnapshot.data()!,
        };
      }
      return null;
    } catch (e) {
      debugPrint('Error getting book by ID: $e');
      return null;
    }
  }
  
  // More methods will be added as needed
}

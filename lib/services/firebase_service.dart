import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

class FirebaseService {
  static Future<void> initializeFirebase() async {
    await Firebase.initializeApp(
      options: const FirebaseOptions(
        apiKey: "AIzaSyA-b4MTWRT8rb6NpOQbqLqKtcURq05YBJ8",
        authDomain: "indian-law-library.firebaseapp.com",
        projectId: "indian-law-library",
        storageBucket: "indian-law-library.firebasestorage.app",
        messagingSenderId: "************",
        appId: "1:************:web:ab981682b704747e32cb91",
        measurementId: "G-SD4RWQPDQQ",
      ),
    );
    
    if (kDebugMode) {
      print('Firebase initialized successfully');
    }
  }
}

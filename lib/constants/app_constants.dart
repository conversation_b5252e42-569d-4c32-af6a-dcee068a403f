import 'package:flutter/material.dart';

class AppConstants {
  // App Information
  static const String appName = 'Indian Law Library';
  static const String packageName = 'com.parultrade.indianlawlibrary';
  
  // Colors
  static const Color primaryColor = Color(0xFF4294FF);
  static const Color secondaryColor = Color(0xFFE6ECFF);
  static const Color backgroundColor = Color(0xFFF8F9FA);
  static const Color textPrimaryColor = Color(0xFF1A1A1A);
  static const Color textSecondaryColor = Color(0xFF666666);
  static const Color errorColor = Color(0xFFE53E3E);
  static const Color successColor = Color(0xFF38A169);
  static const Color warningColor = Color(0xFFD69E2E);
  
  // Firebase Collections
  static const String booksCollection = 'books';
  static const String sectionsCollection = 'sections';
  static const String usersCollection = 'users';
  static const String userProfilesCollection = 'user_profiles';
  static const String userSettingsCollection = 'user_settings';
  static const String userBooksCollection = 'user_books';
  static const String userFavoritesCollection = 'user_favorites';
  static const String userTagsCollection = 'user_tags';
  static const String userTagsSectionsCollection = 'user_tags_sections';
  static const String notificationsCollection = 'notifications';
  static const String userNotificationPreferencesCollection = 'user_notification_preferences';
  static const String userSearchCollection = 'user_search';
  static const String subscriptionsCollection = 'subscriptions';
  
  // Subscription Plans
  static const int freeTrialDays = 90; // 3 months
  static const Map<String, dynamic> subscriptionPlans = {
    '3_months': {'price': 29, 'duration': 90, 'title': '3 Months'},
    '6_months': {'price': 49, 'duration': 180, 'title': '6 Months'},
    '12_months': {'price': 89, 'duration': 365, 'title': '12 Months'},
  };
  
  // Razorpay Configuration
  static const String razorpayKeyId = '***********************';
  static const String razorpayKeySecret = '2rJ8jhSzAQC93TORjw3DfwR7';
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 16.0;
  
  // Text Styles
  static const TextStyle headingStyle = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: textPrimaryColor,
  );
  
  static const TextStyle subHeadingStyle = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: textPrimaryColor,
  );
  
  static const TextStyle bodyStyle = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: textPrimaryColor,
  );
  
  static const TextStyle captionStyle = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: textSecondaryColor,
  );
  
  // Animation Durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);
  
  // API Endpoints (if needed)
  static const String baseUrl = 'https://api.indianlawlibrary.com';
  
  // Shared Preferences Keys
  static const String userIdKey = 'user_id';
  static const String userEmailKey = 'user_email';
  static const String isLoggedInKey = 'is_logged_in';
  static const String themeKey = 'theme_mode';
  static const String subscriptionStatusKey = 'subscription_status';
  static const String freeTrialStartKey = 'free_trial_start';
  static const String lastSyncKey = 'last_sync';
  
  // Error Messages
  static const String networkErrorMessage = 'Please check your internet connection and try again.';
  static const String genericErrorMessage = 'Something went wrong. Please try again.';
  static const String authErrorMessage = 'Authentication failed. Please try again.';
  static const String subscriptionRequiredMessage = 'This feature requires a premium subscription.';
}

import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../constants/app_constants.dart';
import '../services/firestore_service.dart';

class AuthProvider extends ChangeNotifier {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  GoogleSignIn? _googleSignIn;
  
  User? _user;
  bool _isLoading = false;
  String? _errorMessage;
  
  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isLoggedIn => _user != null;
  
  AuthProvider() {
    try {
      _googleSignIn = GoogleSignIn(
        // Add web client ID here when available
        // For now, we'll use default configuration
      );
    } catch (e) {
      debugPrint('Google Sign-In not available: $e');
    }

    _auth.authStateChanges().listen((User? user) {
      _user = user;
      _saveLoginState();
      notifyListeners();
    });
  }
  
  Future<void> _saveLoginState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(AppConstants.isLoggedInKey, isLoggedIn);
      if (_user != null) {
        await prefs.setString(AppConstants.userIdKey, _user!.uid);
        await prefs.setString(AppConstants.userEmailKey, _user!.email ?? '');
      } else {
        await prefs.remove(AppConstants.userIdKey);
        await prefs.remove(AppConstants.userEmailKey);
      }
    } catch (e) {
      debugPrint('Error saving login state: $e');
    }
  }
  
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }
  
  Future<bool> signInWithEmailAndPassword(String email, String password) async {
    try {
      _setLoading(true);
      _setError(null);
      
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      if (credential.user != null) {
        await FirestoreService.updateUserLastLogin(credential.user!.uid);
        return true;
      }
      return false;
    } on FirebaseAuthException catch (e) {
      _setError(_getAuthErrorMessage(e.code));
      return false;
    } catch (e) {
      _setError(AppConstants.genericErrorMessage);
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  Future<bool> createUserWithEmailAndPassword(String email, String password, String name) async {
    try {
      _setLoading(true);
      _setError(null);

      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        // Update display name
        await credential.user!.updateDisplayName(name);

        // Send email verification
        await credential.user!.sendEmailVerification();

        // Create user profile in Firestore
        await FirestoreService.createUserProfile(credential.user!, name);

        return true;
      }
      return false;
    } on FirebaseAuthException catch (e) {
      _setError(_getAuthErrorMessage(e.code));
      return false;
    } catch (e) {
      _setError('Failed to create account: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  Future<bool> signInWithGoogle() async {
    if (_googleSignIn == null) {
      _setError('Google Sign-In not available on this platform');
      return false;
    }

    try {
      _setLoading(true);
      _setError(null);

      final GoogleSignInAccount? googleUser = await _googleSignIn!.signIn();
      if (googleUser == null) {
        return false; // User cancelled the sign-in
      }

      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final userCredential = await _auth.signInWithCredential(credential);

      if (userCredential.user != null) {
        await FirestoreService.createOrUpdateUserProfile(userCredential.user!);
        return true;
      }
      return false;
    } catch (e) {
      _setError(AppConstants.authErrorMessage);
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  Future<bool> resetPassword(String email) async {
    try {
      _setLoading(true);
      _setError(null);
      
      await _auth.sendPasswordResetEmail(email: email);
      return true;
    } on FirebaseAuthException catch (e) {
      _setError(_getAuthErrorMessage(e.code));
      return false;
    } catch (e) {
      _setError(AppConstants.genericErrorMessage);
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  Future<void> signOut() async {
    try {
      if (_googleSignIn != null) {
        await _googleSignIn!.signOut();
      }
      await _auth.signOut();
    } catch (e) {
      debugPrint('Error signing out: $e');
    }
  }

  // User Profile Management
  Future<bool> updateUserProfile({String? displayName, String? photoURL}) async {
    try {
      _setLoading(true);
      _setError(null);

      if (_user != null) {
        if (displayName != null) {
          await _user!.updateDisplayName(displayName);
        }
        if (photoURL != null) {
          await _user!.updatePhotoURL(photoURL);
        }

        // Update in Firestore as well
        await FirestoreService.updateUserProfile(_user!.uid, {
          if (displayName != null) 'displayName': displayName,
          if (photoURL != null) 'photoURL': photoURL,
          'updatedAt': FieldValue.serverTimestamp(),
        });

        // Reload user to get updated info
        await _user!.reload();
        _user = _auth.currentUser;
        notifyListeners();

        return true;
      }
      return false;
    } catch (e) {
      _setError('Failed to update profile: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateEmail(String newEmail) async {
    try {
      _setLoading(true);
      _setError(null);

      if (_user != null) {
        await _user!.updateEmail(newEmail);
        await _user!.sendEmailVerification();
        return true;
      }
      return false;
    } on FirebaseAuthException catch (e) {
      _setError(_getAuthErrorMessage(e.code));
      return false;
    } catch (e) {
      _setError('Failed to update email: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updatePassword(String newPassword) async {
    try {
      _setLoading(true);
      _setError(null);

      if (_user != null) {
        await _user!.updatePassword(newPassword);
        return true;
      }
      return false;
    } on FirebaseAuthException catch (e) {
      _setError(_getAuthErrorMessage(e.code));
      return false;
    } catch (e) {
      _setError('Failed to update password: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> deleteAccount() async {
    try {
      _setLoading(true);
      _setError(null);

      if (_user != null) {
        // Delete user data from Firestore first
        await FirestoreService.deleteUserData(_user!.uid);

        // Delete the user account
        await _user!.delete();
        return true;
      }
      return false;
    } on FirebaseAuthException catch (e) {
      _setError(_getAuthErrorMessage(e.code));
      return false;
    } catch (e) {
      _setError('Failed to delete account: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> sendEmailVerification() async {
    try {
      if (_user != null && !_user!.emailVerified) {
        await _user!.sendEmailVerification();
      }
    } catch (e) {
      debugPrint('Error sending email verification: $e');
    }
  }

  // Getters for user info
  String? get userDisplayName => _user?.displayName;
  String? get userEmail => _user?.email;
  String? get userPhotoURL => _user?.photoURL;
  bool get isEmailVerified => _user?.emailVerified ?? false;
  String? get userId => _user?.uid;
  
  String _getAuthErrorMessage(String code) {
    switch (code) {
      case 'user-not-found':
        return 'No user found with this email address.';
      case 'wrong-password':
        return 'Incorrect password.';
      case 'email-already-in-use':
        return 'An account already exists with this email address.';
      case 'weak-password':
        return 'Password is too weak.';
      case 'invalid-email':
        return 'Invalid email address.';
      case 'user-disabled':
        return 'This account has been disabled.';
      case 'too-many-requests':
        return 'Too many attempts. Please try again later.';
      default:
        return AppConstants.authErrorMessage;
    }
  }
  
  void clearError() {
    _setError(null);
  }
}
